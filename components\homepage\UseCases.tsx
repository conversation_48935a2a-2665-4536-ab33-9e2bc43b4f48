import Image from "next/image";
import React from "react";

const useCases = [
  {
    title: "Fintechs",
    image: "/images/usecases/001.jpg",
    intro:
      "Payout African currencies seamlessly. Fintech companies integrate Flincap's APIs to power:",
    bullets: [
      "Generate and manage virtual named bank accounts",
      "Crypto-to-fiat and fiat-to-crypto exchanges",
      "Generate and manage stablecoin wallets/addresses via API /payout",
    ],
  },
  {
    title: "Large Enterprises & Multinationals",
    image: "/images/usecases/002.jpg",

    intro:
      "Foreign companies in Africa use Flincap for Treasury and FX management:",
    bullets: [
      "Hold value in USD stablecoins",
      "Send and receive funds in African currencies",
      "Repatriate earnings or fund subsidiaries across markets",
    ],
  },
  {
    title: "Marketplaces & Gig Platforms",
    image: "/images/usecases/003.jpg",

    intro:
      "Online marketplaces, freelancing platforms, and gig apps use Flincap to:",
    bullets: [
      "Pay vendors or workers across countries",
      "Use a single integration to send funds to users",
      "Offer crypto or fiat payment to their users",
    ],
  },
  {
    title: "Remittance & Money Transfer Operators (MTOs)",
    image: "/images/usecases/004.jpg",

    intro:
      "MTOs and payment companies use Flincap as their cross-border engine for:",
    bullets: [
      "Instant settlements using stablecoins",
      "Avoiding delays from correspondent banks",
      "Leveraging Flincap's Africa-wide payout network",
    ],
  },
];

export default function UseCases() {
  return (
    <section className="max-w-5xl mx-auto py-16 px-4 font-circularStd">
      <h2 className="text-3xl md:text-4xl font-bold text-[#0A2540] text-center">
        Use Cases
      </h2>
      <p className="text-center text-gray-600 max-w-2xl mx-auto mt-3">
        How different organizations use Flincap to move value across borders.
      </p>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-10 mt-12">
        {useCases.map((c, idx) => (
          <div key={c.title} className="flex flex-col gap-6">
            <div className="relative inline-block after:content-[''] after:absolute after:top-[0.3rem] after:left-[0.3rem] after:w-full after:h-full after:rounded-xl after:bg-purple-600">
              <div className="rounded-xl overflow-hidden border-[3px] border-black border-solid relative z-10">
                <Image
                  src={c.image}
                  alt={c.title}
                  width={540}
                  height={340}
                  className="w-full h-40 md:h-52 object-cover block"
                />
              </div>
            </div>

            <div className="flex-1">
              <h3 className="text-lg font-semibold text-[#111827]">
                {c.title}
              </h3>
              <p className="text-sm text-gray-600 mt-2">{c.intro}</p>

              <ul className="mt-3 ml-4 list-disc text-sm text-gray-700 space-y-1">
                {c.bullets.map((b) => (
                  <li key={b}>{b}</li>
                ))}
              </ul>
            </div>
          </div>
        ))}
      </div>
    </section>
  );
}
