"use client";

import React from "react";
import Link from "next/link";
import { Plus, Minus } from "lucide-react";
import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
  AccordionContent,
} from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";

const faqs = [
  {
    id: "q1",
    question: "What is Flincap and how does it work?",
    answer:
      "Flincap is a borderless payments platform that enables businesses to send, receive, and manage payments across Africa in both local currencies and stablecoins. You can use our APIs, hosted checkout pages, and virtual accounts to collect local payments, settle in stablecoins or USD, and make payouts across multiple African countries from one account.",
  },
  {
    id: "q2",
    question: "Which countries and currencies does Flincap support?",
    answer:
      "Flincap currently supports payments in Nigeria, Ghana, Kenya, and South Africa, with more countries being added soon. You can collect and pay out in local currencies such as NGN, GHS, KES, and ZAR, while holding your core balance in stablecoins like USDC or USDT.",
  },
  {
    id: "q3",
    question: "Do I need a local entity in each country to use Flincap?",
    answer:
      "No. With Flincap, you can issue local bank accounts or mobile money references without opening a physical branch or registering a local entity. Your customers or partners pay locally, and you receive the funds in stablecoins or USD credit in your Flincap account.",
  },
  {
    id: "q4",
    question: "How secure and compliant is Flincap?",
    answer:
      "We partner with licensed financial institutions and follow all relevant local regulations. All transactions go through required KYC and AML checks, and stablecoin settlements are conducted via trusted, regulated partners. Your funds and data are protected with enterprise-grade security",
  },
  {
    id: "q5",
    question: "Can I integrate Flincap with my existing systems?",
    answer:
      "Yes. Flincap offers developer-friendly APIs for stablecoin payments, payouts, and collections. If you don’t want to integrate, you can use our hosted checkout pages or payment links. We also provide reconciliation tools so you can match transactions with invoices or customer records in your own systems.",
  },
];

export default function Faq() {
  return (
    <section className="max-w-7xl mx-auto px-6 py-20 font-circularStd">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
        {/* Left column */}
        <div>
          <p className="text-sm text-[#02BCF5] font-medium mb-3">
            Frequently Asked Questions
          </p>
          <h2 className="text-4xl font-extrabold tracking-tight text-[#0b2540] mb-4">
            Get to know us better
          </h2>
          <p className="text-lg text-slate-600 max-w-xl leading-relaxed mb-6">
            Have a question that is not answered? You can contact us at{" "}
            <a
              href="mailto:<EMAIL>"
              className="text-purple-600 font-medium underline"
            >
              <EMAIL>
            </a>
            .
          </p>

          <div className="mt-6">
            <a
              href="mailto:<EMAIL>"
              className="px-6 py-3 bg-[#7928FF] text-white rounded-md"
            >
              Contact Us
            </a>
          </div>
        </div>

        {/* Right column - Accordion */}
        <div>
          <Accordion type="single" collapsible className="space-y-4">
            {faqs.map((f, idx) => (
              <AccordionItem
                value={f.id}
                key={f.id}
                className="relative border-none"
              >
                <AccordionTrigger
                  className={`w-full rounded-lg border border-black/10 py-5 px-6 text-left
                    flex items-center justify-between gap-4 bg-white shadow-sm
                    hover:shadow-md transition
                    data-[state=open]:bg-gradient-to-br data-[state=open]:from-purple-600 data-[state=open]:to-purple-500
                    data-[state=open]:text-white data-[state=open]:shadow-lg`}
                >
                  <span className="font-medium text-base leading-tight">
                    {f.question}
                  </span>
                </AccordionTrigger>

                <AccordionContent className="mt-2 rounded-b-xl border border-t-0 border-black/5 bg-white p-6 text-slate-700 data-[state=open]:bg-transparent data-[state=open]:text-white">
                  <p className="leading-relaxed">{f.answer}</p>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
      </div>
    </section>
  );
}
