"use client";

import React from "react";
import Link from "next/link";
import { Plus, Minus } from "lucide-react";
import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
  AccordionContent,
} from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";

const faqs = [
  {
    id: "q1",
    question: "What is Flincap and how does it work?",
    answer:
      "Flincap is a borderless payments platform that enables businesses to send, receive, and manage payments across Africa in both local currencies and stablecoins. You can use our APIs, hosted checkout pages, and virtual accounts to collect local payments, settle in stablecoins or USD, and make payouts across multiple African countries from one account.",
  },
  {
    id: "q2",
    question: "Which countries and currencies does Flincap support?",
    answer:
      "We support a growing set of African countries and local currencies. Reach out to support to confirm availability for your country and currency pair. We can also offer tailored onboarding for enterprise flows.",
  },
  {
    id: "q3",
    question: "Do I need a local entity in each country to use Flincap?",
    answer:
      "No. In many cases you can use Flincap without a local entity — we provide virtual accounts and payout rails that remove the need for you to set up local entities. For complex regulatory or treasury requirements we can discuss bespoke setups.",
  },
  {
    id: "q4",
    question: "How secure and compliant is Flincap?",
    answer:
      "Security and compliance are central to our platform. We follow industry best practices for data protection and transaction security, and we work with local partners to ensure regulatory compliance in each market we operate in.",
  },
  {
    id: "q5",
    question: "Can I integrate Flincap with my existing systems?",
    answer:
      "Yes. Flincap offers developer-friendly REST APIs and SDKs that let you integrate payments, payouts, virtual accounts, and stablecoin wallets into your existing systems with minimal effort.",
  },
];

export default function Faq() {
  return (
    <section className="max-w-7xl mx-auto px-6 py-20 font-circularStd">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
        {/* Left column */}
        <div>
          <p className="text-sm text-[#02BCF5] font-medium mb-3">
            Frequently Asked Questions
          </p>
          <h2 className="text-4xl font-extrabold tracking-tight text-[#0b2540] mb-4">
            Get to know us better
          </h2>
          <p className="text-lg text-slate-600 max-w-xl leading-relaxed mb-6">
            Have a question that is not answered? You can contact us at{" "}
            <a
              href="mailto:<EMAIL>"
              className="text-purple-600 font-medium underline"
            >
              <EMAIL>
            </a>
            .
          </p>

          <div className="mt-6">
            <Button asChild className="bg-[#7928FF]">
              <Link href="/contact" className="px-6 py-3 ">
                Contact Us
              </Link>
            </Button>
          </div>
        </div>

        {/* Right column - Accordion */}
        <div>
          <Accordion type="single" collapsible className="space-y-4">
            {faqs.map((f, idx) => (
              <AccordionItem
                value={f.id}
                key={f.id}
                className="relative border-none"
              >
                <AccordionTrigger
                  className={`w-full rounded-lg border border-black/10 py-5 px-6 text-left
                    flex items-center justify-between gap-4 bg-white shadow-sm
                    hover:shadow-md transition
                    data-[state=open]:bg-gradient-to-br data-[state=open]:from-purple-600 data-[state=open]:to-purple-500
                    data-[state=open]:text-white data-[state=open]:shadow-lg`}
                >
                  <span className="font-medium text-base leading-tight">
                    {f.question}
                  </span>
                </AccordionTrigger>

                <AccordionContent className="mt-2 rounded-b-xl border border-t-0 border-black/5 bg-white p-6 text-slate-700 data-[state=open]:bg-transparent data-[state=open]:text-white">
                  <p className="leading-relaxed">{f.answer}</p>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
      </div>
    </section>
  );
}
