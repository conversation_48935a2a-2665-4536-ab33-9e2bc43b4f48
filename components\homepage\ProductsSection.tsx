import Link from "next/link";
import Image from "next/image";
import { Reveal } from "../animations/animations";
import { But<PERSON> } from "@/components/ui/button";

export default function Products() {
  return (
    <section className="py-16 font-circularStd">
      <h2 className="text-center text-3xl font-extrabold text-[#0A2540]">
        Products
      </h2>
      <p className="text-center mb-2 py-3 text-[#425466]">
        Enabling global payments, one business at a time
      </p>
      <Reveal>
        <div className="lg:max-w-7xl max-w-[90%] mx-auto gap-4 grid grid-cols-1 md:grid-cols-4 md:max-h-[450px]">
          <Link
            href="/"
            className="bg-[#7928FF] border-black border-solid border-2 text-white px-7 py-6 mx-auto rounded-2xl md:px-10 w-full"
          >
            <h3 className="font-extrabold text-lg py-3 font-circularStd">
              Payouts
            </h3>
            <p className="text-sm font-circularStd lg:max-w-64">
              Facilitates global money transfers.
            </p>
          </Link>
          <Link
            href="/"
            className="bg-[#70BBFF] border-black border-solid border-2 p-7 mx-auto rounded-2xl md:px-10 md:w-full"
          >
            <h3 className="font-extrabold text-lg py-3 font-circularStd">
              Collections
            </h3>

            <p className="text-sm font-circularStd lg:max-w-64">
              Provides instant stablecoins onramp and offramp..
            </p>
          </Link>
          <Link
            href="/"
            className="bg-[#01D797] border-black border-solid border-2 px-7 py-6 mx-auto rounded-2xl md:px-10 md:w-full"
          >
            <h3 className="font-extrabold text-lg py-3 font-circularStd">
              OTC Desk
            </h3>
            <p className="text-sm font-circularStd lg:max-w-64">
              Enables businesses to make collections and payouts in local
              African currencies.
            </p>
          </Link>
          <Link
            href="/"
            className="bg-[#E0C602] border-black border-solid border-2 px-7 py-6 mx-auto rounded-2xl md:px-10 md:w-full"
          >
            <h3 className="font-extrabold text-lg py-3 font-circularStd">
              Wallets API
            </h3>
            <p className="text-sm font-circularStd lg:max-w-64">
              Enables businesses to make collections and payouts in local
              African currencies.
            </p>
          </Link>
        </div>

        <div className="flex justify-center mt-8">
          <Button asChild className="bg-[#7928FF]">
            <Link href="/products" className="px-6 py-3">
              Learn more
            </Link>
          </Button>
        </div>
      </Reveal>
    </section>
  );
}
