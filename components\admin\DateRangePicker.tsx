"use client";

import * as React from "react";
import { format } from "date-fns";
import { Calendar } from "../ui/calendar";
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { CalendarIcon } from "lucide-react";
import { DateRange } from "react-day-picker";

export function DateRangePicker({
  date,
  setDate,
  fetchAnalyticsData
}: {
  date: DateRange | undefined;
  setDate: (date: DateRange | undefined) => void;
  fetchAnalyticsData: () => void;
}) {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className="w-[280px] justify-start text-left font-normal"
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {date?.from ? (
            date.to ? (
              <>
                {format(date.from, "yyyy-MM-dd")} →{" "}
                {format(date.to, "yyyy-MM-dd")}
              </>
            ) : (
              format(date.from, "yyyy-MM-dd")
            )
          ) : (
            <span>Select date range</span>
          )}
          

        </Button>
      </PopoverTrigger>
      {date?.from || date?.to ? (
  <Button
  variant="ghost"
  size="sm"
  onClick={() => {
    setDate(undefined);
    fetchAnalyticsData(); 
  }}
>
  Clear
</Button>


) : null}
      <PopoverContent className="w-auto p-0" align="start">
  <div className="flex flex-col gap-2 p-2">
    <Calendar
      mode="range"
      selected={date}
      onSelect={setDate}
      numberOfMonths={2}
    />
    {(date?.from || date?.to) && (
      <Button
        variant="ghost"
        size="sm"
        onClick={() => {
          setDate(undefined);
          fetchAnalyticsData(); // Now it works after clearing
        }}
      >
        Clear
      </Button>
    )}
  </div>
</PopoverContent>
    </Popover>
  );
}
