"use client";
import type { Metadata } from "next";
import { ToastContainer } from "react-toastify";
import "./globals.css";
import "react-toastify/dist/ReactToastify.css";
import ModalLayout from "@/components/layouts/ModalLayout";
import { circularStd } from "@/assets/fonts/font";
import { InformationCircleIcon } from "@/components/icons";
import { Toaster } from "@/components/ui/sonner";
import { createContext, useContext, useEffect, useState } from "react";
import { api } from "./utils";

const flincapDescription = `Set up a crypto exchange in seconds.`;

const metadata: Metadata = {
  metadataBase: new URL(
    process.env.NEXT_PUBLIC_FLINCAP_APP_URL || "https://flincap.com"
  ),
  title: {
    default: "Cross Border Payments | Flincap",
    template: "%s | Flincap",
  },
  // description: flincapDescription,
  keywords:
    "otc, crypto, wallets, automated, exchange, ease, flincap, fiat, payments",
  twitter: {
    title: {
      default: "Cross Border Payments | Flincap",
      template: "%s | Flincap",
    },
    images: {
      url: "https://flincap.com/images/flincap-icon.png",
    },
    description: flincapDescription,
    card: "summary",
  },
  applicationName: "Flincap",
  openGraph: {
    title: {
      default: "Cross Border Payments | Flincap",
      template: "%s | Flincap",
    },
    // description: flincapDescription,
    images: {
      url: "https://flincap.com/images/flincap-icon.png",
    },
    tags: "otc, crypto, wallets, automated, exchange, ease, flincap, fiat, payments",
  },
};

const contextClass = {
  success: "bg-blue-600",
  error: "bg-red-600",
  info: "bg-gray-600",
  warning: "bg-orange-400",
  default: "bg-indigo-600",
  dark: "bg-white-600 font-gray-300",
};

const UserDetailsContext = createContext<{ bankData: any } | null>(null);

export function useUserDetailsContext() {
  const context = useContext(UserDetailsContext);
  if (!context)
    throw new Error("useDashboardData must be used within DashboardLayout");
  return context;
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [bankData, setBankData] = useState({
    account_name: "",
    account_number: "",
    bank_code: "",
    bank_name: "",
    balance: 0,
  });
  useEffect(() => {
    if (typeof window != "undefined") {
      api
        .get("/v1/wallets/dashboard-data")
        .then((res) => {
          const fiat = res.data.data.wallets.find(
            (wallet: any) => wallet.walletType === "FIAT"
          );
          setBankData({
            account_name: fiat.account_name,
            account_number: fiat.account_num,
            bank_code: fiat.bank_code,
            bank_name: fiat.bank_name,
            balance: fiat.balance,
          });
        })
        .catch((err) => {
          console.log(err);
        });
    }
  }, []);
  return (
    <html lang="en">
      <UserDetailsContext.Provider value={{ bankData }}>
        <body className={`${circularStd.variable} antialiased`}>
          <ToastContainer
            autoClose={1500}
            hideProgressBar
            toastClassName={""}
            icon={<InformationCircleIcon className="stroke-appPurple" />}
          />
          <div>{children}</div>
          <Toaster position="top-right" richColors />
          <ModalLayout />
        </body>
      </UserDetailsContext.Provider>
    </html>
  );
}
