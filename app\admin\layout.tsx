"use client";
import DashboardN<PERSON> from "@/components/DashboardNav";
import Image from "next/image";
import Link from "next/link";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { ReactNode, useEffect, useState } from "react";
import { CONSTANTS, adminApi } from "../utils";
import LoadingSpinner from "@/components/LoadingSpinner";
import useStore, { StoreState } from "@/store";
import axios from "axios";

function Layout({ children }: { children: ReactNode }) {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [showProductLinks, setShowProductLinks] = useState(true);
  const [showQuickLinks, setShowQuickLinks] = useState(true);
  const { collapsedMenu, user, setCollapsedMenu, setUser } = useStore(
    (state: StoreState) => state
  );
  const [showLinks, setShowLinks] = useState<"products" | "quick" | "none">(
    "products"
  );
  const [loading, setLoading] = useState(true);
  const navigate = useRouter();

  useEffect(() => {
    const auth_token = searchParams.get("auth_token");

    async function getAccount() {
      try {
        const res = await adminApi.get("/v1/auth");
        setUser(res.data.data);

        if (!res.data.data.isAdmin) {
          // Redirect non-admin users to login
          window.location.href =
            "/auth/login?redirect_uri=" + window.location.href;
          return;
        }
        setLoading(false);
        navigate.replace(pathname);
      } catch (err: any) {
        if (err.response) {
          console.log(err.response);
          if (
            err.response.status === 403 ||
            err.response.statusText === "Forbidden"
          ) {
            // Redirect unauthorized users to login
            window.location.href =
              "/auth/login?redirect_uri=" + window.location.href;
            return;
          }
        }
        console.log(err);
      } finally {
        setLoading(false);
      }
    }

    async function verifyOTP() {
      try {
        const res = await axios.get(
          CONSTANTS.SERVER_URL + "/v1/auth/verify-login",
          {
            params: {
              auth_token,
            },
          }
        );
        if (res.status.toString().startsWith("2")) {
          localStorage.setItem("tid", res.data.data.accessToken);
          // window.location.href = "/dashboard";
          getAccount();
        } else {
          console.log("Login failed");
        }
      } catch (err: any) {
        console.log(err);
        if (err.response) {
          console.log(err.response.data);
        }
      }
    }

    if (auth_token) {
      verifyOTP();
    } else {
      getAccount();
    }
  }, [navigate, pathname, searchParams, setUser]);

  const hideSideMenu = (e: any) => {
    if ((e.target as any).classList.contains("mainDrop")) {
      setCollapsedMenu(false);
    }
  };

  return (
    <>
      {loading ? (
        <div className="flex justify-center items-center h-screen">
          <LoadingSpinner />
        </div>
      ) : (
        <div>
          <DashboardNav />

          <div className="bg-[#1c1c1c] min-h-[calc(100vh-3.5rem)] px-4 text-sm pt-16 md:pt-0 pb-2">
            <div className="mx-auto max-w-screen-2xl">
              <nav className="flex flex-wrap items-center justify-between">
                <div className="profileData flex gap-2 items-center h-[58px] pb-2 text-white">
                  <Image
                    src={
                      user?.image
                        ? user?.image
                        : "https://ui-avatars.com/api/?name=" + user?.fullName
                    }
                    width={38}
                    height={38}
                    className="rounded-full"
                    alt="profile foto"
                  />
                  <div
                    hidden={collapsedMenu}
                    className="textdata rounded-md px-2.5 py-1 cursor-pointer no-select"
                  >
                    <h3 className="text-sm">{user?.fullName}</h3>
                    <div className="flex gap-1 text-xs">
                      <p>Flincap admin</p>
                    </div>
                  </div>
                </div>

                <ul className="text-white flex items-center gap-5 overflow-x-auto overflow-y-hidden">
                  <li>
                    <Link
                      href="/admin"
                      className={`border-b-[3.5px] py-[18.5px] ${
                        pathname === "/admin"
                          ? "border-b-[#c2a8ef] text-[#c2a8ef]"
                          : "border-b-transparent"
                      }`}
                    >
                      Overview
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/admin/clients"
                      className={`border-b-[3.5px] py-[18.5px] ${
                        pathname.startsWith("/admin/clients")
                          ? "border-b-[#c2a8ef] text-[#c2a8ef]"
                          : "border-b-transparent"
                      }`}
                    >
                      Clients
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/admin/approvals"
                      className={`border-b-[3.5px] py-[18.5px] ${
                        pathname.startsWith("/admin/approvals")
                          ? "border-b-[#c2a8ef] text-[#c2a8ef]"
                          : "border-b-transparent"
                      }`}
                    >
                      Approvals
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/admin/wallets"
                      className={`border-b-[3.5px] py-[18.5px] ${
                        pathname.startsWith("/admin/wallets")
                          ? "border-b-[#c2a8ef] text-[#c2a8ef]"
                          : "border-b-transparent"
                      }`}
                    >
                      Wallets
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/admin/transactions"
                      className={`border-b-[3.5px] py-[18.5px] ${
                        pathname.startsWith("/admin/transactions")
                          ? "border-b-[#c2a8ef] text-[#c2a8ef]"
                          : "border-b-transparent"
                      }`}
                    >
                      Transactions
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/admin/tickets"
                      className={`border-b-[3.5px] py-[18.5px] ${
                        pathname.startsWith("/admin/tickets")
                          ? "border-b-[#c2a8ef] text-[#c2a8ef]"
                          : "border-b-transparent"
                      }`}
                    >
                      Tickets
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/admin/settings"
                      className={`border-b-[3.5px] py-[18.5px] ${
                        pathname.startsWith("/admin/settings")
                          ? "border-b-[#c2a8ef] text-[#c2a8ef]"
                          : "border-b-transparent"
                      }`}
                    >
                      Settings
                    </Link>
                  </li>
                </ul>
              </nav>

              <main className=" bg-white max-w-[calc(100vw-30px)] md:max-w-screen-2xl rounded-xl grow min-h-[calc(49rem-90px)] md:mt-0 mt-4">
                {children}
              </main>
            </div>
          </div>
        </div>
      )}
    </>
  );
}

export default Layout;
