"use client";

import React from "react";
import { X } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

interface PrivacyPolicyModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function PrivacyPolicyModal({ isOpen, onClose }: PrivacyPolicyModalProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white rounded-lg shadow-lg max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-2xl font-bold text-gray-900">Privacy Policy</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          <div className="prose max-w-none">
            <div className="mb-6">
              <h3 className="text-xl font-semibold text-appPurple mb-2">Flincap</h3>
              <p className="text-gray-700 leading-relaxed">
                At Flincap, your privacy matters to us. This Privacy Policy explains how we collect, 
                use, and protect your information when you engage with our onboarding process or use our services.
              </p>
            </div>

            <div className="mb-6">
              <h4 className="text-lg font-semibold text-gray-900 mb-3">Who We Are</h4>
              <p className="text-gray-700 leading-relaxed">
                Flincap is a B2B stablecoin payments company helping businesses send, receive, and settle 
                cross-border payments across Africa with speed, security, and compliance.
              </p>
            </div>

            <div className="mb-6">
              <h4 className="text-lg font-semibold text-gray-900 mb-3">Information We Collect</h4>
              <p className="text-gray-700 leading-relaxed mb-3">
                When you click Sign Up and complete our onboarding form, we collect the following details:
              </p>
              <ul className="list-disc list-inside text-gray-700 space-y-1 ml-4">
                <li>Full Name</li>
                <li>Email Address</li>
                <li>Contact Number</li>
                <li>Company Name</li>
                <li>Location (City & Country)</li>
              </ul>
              <p className="text-gray-700 leading-relaxed mt-3">
                If your application is approved, we may later request KYC documents (e.g., BVN), which are 
                processed securely by our licensed third-party providers.
              </p>
            </div>

            <div className="mb-6">
              <h4 className="text-lg font-semibold text-gray-900 mb-3">How We Use Your Information</h4>
              <p className="text-gray-700 leading-relaxed mb-3">We use the information provided to:</p>
              <ul className="list-disc list-inside text-gray-700 space-y-1 ml-4">
                <li>Review and decide eligibility for a Flincap business account.</li>
                <li>Understand your business needs and product interests.</li>
                <li>Communicate with you about your application status.</li>
              </ul>
            </div>

            <div className="mb-6">
              <h4 className="text-lg font-semibold text-gray-900 mb-3">Sharing of Information</h4>
              <p className="text-gray-700 leading-relaxed mb-3">Your information may be shared only with:</p>
              <ul className="list-disc list-inside text-gray-700 space-y-1 ml-4">
                <li>Third-party compliance/KYC providers (to verify your identity and issue account numbers).</li>
                <li>Service providers and regulators, when legally required.</li>
              </ul>
              <p className="text-gray-700 leading-relaxed mt-3">
                We do not sell or rent your information to marketers or unrelated third parties.
              </p>
            </div>

            <div className="mb-6">
              <h4 className="text-lg font-semibold text-gray-900 mb-3">Data Retention</h4>
              <p className="text-gray-700 leading-relaxed">
                We retain your onboarding information only as long as needed for application review, account setup, 
                and regulatory compliance. If your application is not approved, we will securely delete your data 
                after a reasonable period unless the law requires otherwise.
              </p>
            </div>

            <div className="mb-6">
              <h4 className="text-lg font-semibold text-gray-900 mb-3">Security</h4>
              <p className="text-gray-700 leading-relaxed">
                We apply strict technical and organizational measures to safeguard your information, including 
                encryption, access controls, and secure third-party integrations.
              </p>
            </div>

            <div className="mb-6">
              <h4 className="text-lg font-semibold text-gray-900 mb-3">Your Rights</h4>
              <p className="text-gray-700 leading-relaxed mb-3">You have the right to:</p>
              <ul className="list-disc list-inside text-gray-700 space-y-1 ml-4">
                <li>Access and request a copy of your personal data.</li>
                <li>Request corrections to inaccurate information.</li>
                <li>Withdraw consent (where applicable).</li>
                <li>Request deletion, subject to regulatory obligations.</li>
              </ul>
              <p className="text-gray-700 leading-relaxed mt-3">
                To exercise these rights, contact us at{" "}
                <a href="mailto:<EMAIL>" className="text-appPurple hover:underline">
                  <EMAIL>
                </a>
                .
              </p>
            </div>

            <div className="mb-6">
              <h4 className="text-lg font-semibold text-gray-900 mb-3">Updates to This Policy</h4>
              <p className="text-gray-700 leading-relaxed">
                We may update this Privacy Policy from time to time. Changes will be posted here with a new effective date.
              </p>
              <p className="text-gray-700 leading-relaxed mt-3 font-medium">
                Date of update: 3rd September 2025
              </p>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end p-6 border-t bg-gray-50">
          <Button onClick={onClose} className="bg-appPurple hover:bg-appPurpleDark">
            Close
          </Button>
        </div>
      </div>
    </div>
  );
}
