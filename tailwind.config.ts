import type { Config } from "tailwindcss";

const config = {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{ts,tsx}",
    "./components/**/*.{ts,tsx}",
    "./app/**/*.{ts,tsx}",
    "./src/**/*.{ts,tsx}",
  ],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
      fontFamily: {
        circularStd: ["var(--circularStd)"],
        inter: ["var(--inter)"],
      },
      colors: {
        // New colors
        appWhite: "#f2ebff",
        appWhite2: "#fcfcfc",
        appPurple: "#8237ff",
        appPurpleDark: "#6c22e4",
        primary: "#7928FF",
        appGrayText: "#424242",
        appGrayText2: "#656565",
        appGrayTextLight: "#8c8c8c",

        ash1: "#F8F8F8",
        ash: "#637381",
        ash2: "#DADCE0",
        navy: "#202b47",
      },
      gridTemplateColumns: {
        cards: "repeat(auto-fit, minmax(300px, 1fr))",
      },
      boxShadow: {
        card: "rgba(149, 157, 165, 0.2) 0px 0px 52px",
      },
      backgroundImage: {
        "chevron-down": "url('/images/chevron-down.svg')",
        "gradient-radial": "radial-gradient(var(--tw-gradient-stops))",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
} satisfies Config;

export default config;
