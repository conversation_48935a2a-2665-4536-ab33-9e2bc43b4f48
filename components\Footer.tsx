import Image from "next/image";
import Link from "next/link";
import React, { useState } from "react";
import {
  FaFacebook,
  FaInstagram,
  FaLinkedin,
  FaTwi<PERSON>,
} from "react-icons/fa6";
import PrivacyPolicyModal from "./modals/PrivacyPolicyModal";

function Footer() {
  const [isPrivacyModalOpen, setIsPrivacyModalOpen] = useState(false);

  const handlePrivacyClick = () => {
    setIsPrivacyModalOpen(true);
  };

  const handleClosePrivacyModal = () => {
    setIsPrivacyModalOpen(false);
  };

  return (
    <>
      {/* <footer className="bg-white">
    //   <div className="max-w-7xl mx-auto flex-col lg:flex-row gap-6 px-2 py-10 lg:px-0">
    // <Link href={"/"} className="logo lg:mr-2 mb-4">
    //   <Image
    //     src={"/images/flincap-logo.svg"}
    //     alt="."
    //     width={100}
    //     height={50}
    //   />
    // </Link>
    //     <div className="flex items-center gap-4 mb-2 mt-4">
    //       <p className="font-normal">
    //         Bridging the Future of Global Transactions Using Stablecoins.
    //       </p>{" "}
    //       <Link href={"https://linkedin.com/company/flincaphq"}>
    //         <LinkedInIcon />
    //       </Link>
    //       <Link href={"https://twitter.com/flincaphq"}>
    //         <TwitterIcon />
    //       </Link>
    //     </div>
    //     <div className="mt-4">
    //       <p className="font-normal">8 The Green, Ste R</p>
    //       <p className="font-normal">Dover County, 19901, Delaware, US.</p>
    //       <div className="flex gap-2 mt-2">
    //         <p className="font-normal">+1 (239) 744-3519 | </p>
    //         <p className="font-normal">
    //           <a
    //             href="mailto:<EMAIL>"
    //             className="text-blue-500 hover:underline"
    //           >
    //             <EMAIL>
    //           </a>
    //         </p>
    //       </div>
    //     </div>
    //   </div>
    // </footer> */}
      <footer className="bg-gradient-to-r font-circularStd from-[#24043e] via-[#2a0450] to-[#2a0b5a] text-white">
        <div className="max-w-7xl mx-auto px-6 py-16">
          <div className="flex flex-col md:flex-row md:items-start md:justify-between gap-10">
            {/* Left - logo, tagline, socials */}
            <div className="max-w-md">
              <Link href={"/"} className="logo lg:mr-2 mb-4">
                <Image
                  src={"/images/flincap-logo-normal.svg"}
                  alt="."
                  width={100}
                  height={50}
                />
              </Link>

              <p className="mt-6 text-white/90 leading-relaxed">
                Enabling global payments, one business at a time
              </p>

              <div className="flex items-center gap-3 mt-6">
                <Link href={"https://linkedin.com/company/flincaphq"}>
                  <FaLinkedin className="w-6 h-6" />
                </Link>
                <Link href={"https://facebook.com/flincaphq"}>
                  <FaFacebook className="w-6 h-6" />
                </Link>
                <Link href={"https://instagram.com/flincaphq"}>
                  <FaInstagram className="w-6 h-6" />
                </Link>
                <Link href={"https://twitter.com/flincaphq"}>
                  <FaTwitter className="w-6 h-6" />
                </Link>
              </div>

              <p className="mt-6 text-sm text-white/60">
                © {new Date().getFullYear()} Towerbase Technologies. All rights
                reserved.
              </p>
            </div>

            {/* Right - link columns */}
            <div className="w-full md:w-auto flex-1 md:flex-none">
              <div className="grid grid-cols-2 md:grid-cols-3 gap-8">
                <div>
                  <h4 className="text-sm font-semibold mb-4">Company</h4>
                  <ul className="space-y-3 text-sm text-white/70">
                    <li>
                      <Link
                        href="https://substack.com/@flincapnewsletter?utm_campaign=profile&utm_medium=profile-page"
                        className="hover:text-white"
                      >
                        Blog
                      </Link>
                    </li>
                    <li>
                      <Link href="/products" className="hover:text-white">
                        Products
                      </Link>
                    </li>
                    <li>
                      <a href="mailto:<EMAIL>" className="">
                        Contact Us
                      </a>
                    </li>
                  </ul>
                </div>

                <div>
                  <h4 className="text-sm font-semibold mb-4">Resources</h4>
                  <ul className="space-y-3 text-sm text-white/70">
                    {/* <li>
                    <Link href="#" className="hover:text-white">
                      Developers
                    </Link>
                  </li> */}
                    <li>
                      <button
                        onClick={handlePrivacyClick}
                        className="hover:text-white text-left"
                      >
                        Privacy Policy
                      </button>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </footer>

      {/* Privacy Policy Modal */}
      <PrivacyPolicyModal
        isOpen={isPrivacyModalOpen}
        onClose={handleClosePrivacyModal}
      />
    </>
  );
}

export default Footer;
