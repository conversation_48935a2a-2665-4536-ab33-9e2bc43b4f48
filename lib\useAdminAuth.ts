"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { adminApi, getToken } from "@/app/utils";
import useStore from "@/store";

export function useAdminAuth() {
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);
  const router = useRouter();
  const { setUser } = useStore();

  useEffect(() => {
    async function checkAuth() {
      try {
        // Check if token exists
        const token = getToken();
        if (!token) {
          redirectToLogin();
          return;
        }

        // Verify admin authentication
        const res = await adminApi.get("/v1/auth");
        const userData = res.data.data;
        
        if (!userData) {
          redirectToLogin();
          return;
        }

        // Check if user is admin
        if (!userData.isAdmin) {
          redirectToLogin();
          return;
        }

        // User is authenticated and is admin
        setUser(userData);
        setIsAuthenticated(true);
        setIsAdmin(true);
        setIsLoading(false);

      } catch (error: any) {
        console.error("Admin auth check failed:", error);
        
        // Handle different error cases
        if (error.response?.status === 403 || error.response?.status === 401) {
          redirectToLogin();
        } else {
          // For other errors, still redirect to be safe
          redirectToLogin();
        }
      }
    }

    checkAuth();
  }, [router, setUser]);

  const redirectToLogin = () => {
    const currentUrl = window.location.href;
    window.location.href = `/auth/login?redirect_uri=${encodeURIComponent(currentUrl)}`;
  };

  return {
    isLoading,
    isAuthenticated,
    isAdmin,
  };
}
