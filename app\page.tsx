"use client";

import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import Nav from "@/components/Nav";
import Footer from "@/components/Footer";
import CookieConsent from "@/components/CookieConsent";
import { CONSTANTS, getToken } from "./utils";
import axios from "axios";
import { useEffect, useState } from "react";
import ImagesCarousel from "@/components/Carousel";
import Header from "@/components/homepage/NewHeader";
import Products from "@/components/homepage/ProductsSection";
import BlackBanner from "@/components/homepage/BlackBanner";
import UseCases from "@/components/homepage/UseCases";
import Faq from "@/components/homepage/Faq";

export default function Home() {
  const [user, setUser] = useState(null);

  useEffect(() => {
    async function fetchAuth() {
      try {
        const res = await axios.get(CONSTANTS.SERVER_URL + "/v1/auth", {
          headers: { Authorization: "Bearer " + getToken() },
        });
        if (res.status === 403) return;
        else setUser(res.data.data);
        console.log(res.data.data);
      } catch (err: any) {
        if (err.response) {
          if (err.response.status === 403) {
            // ------ Not logged in
          }
        }
        console.log(err);
      }
    }

    fetchAuth();
  }, []);

  return (
    <>
      <Nav />
      <CookieConsent />
      <Header user={user} />
      <Products />
      <UseCases />
      <Faq />
      <FlincapAtEvents />
      <Footer />
    </>
  );
}

function FlincapInSixtySecs() {
  return (
    <section className="bg-[#F0EFFF]">
      <div className="max-w-7xl mx-auto py-16 px-4">
        {/* <h2 className="text-center text-3xl font-bold pb-5 text-[#0A2540]">
          Flincap in 60 Seconds
        </h2> */}
        <iframe
          className="mt-4 mx-auto md:mt-0 max-w-[850px] w-full rounded-xl h-[400px] md:h-[550px] bg-white"
          src="https://www.youtube.com/embed/0RupPtBruB4"
          title="Flincap's explanatory video"
          frameBorder="0"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
          allowFullScreen
        ></iframe>
      </div>
    </section>
  );
}

function FlincapAtEvents() {
  return (
    <section className="max-w-7xl mx-auto py-16 font-circularStd">
      <h2 className="text-4xl leading-10 my-14 font-bold text-[#282735]">
        Flincap at Events
      </h2>
      <ImagesCarousel />
    </section>
  );
}
