import useStore from "@/store";
import React, { FormEvent, useEffect, useState } from "react";
import { ChevronDownIcon, XMarkIcon } from "../icons";
import LoadingSpinner from "../LoadingSpinner";
import { iWalletSymbol } from "@/types/utils";
import { api, getTokenName } from "@/app/utils";
import CryptoIcon from "../CryptoIcon";
import SuccessModal from "./SuccessModal";
import { ToastContainer, toast } from "react-toastify";
import Select from "react-select";

function WithdrawCryptoModal({
  wallets,
  baseCurrency,
}: {
  wallets: any;
  baseCurrency: string;
}) {
  console.log(wallets);
  const { setModal } = useStore();
  const [loading, setLoading] = useState(false);
  const [formDetails, setFormDetails] = useState({
    amount: 0,
    address: "",
  });
  const [receivedAmount, setReceivedAmount] = useState(0);
  const [fee, setFee] = useState(0);
  const [otp, setOtp] = useState("");
  const [isOtpAvailable, setisOtpAvailable] = useState(false);
  const [wallet, setWallet] = useState<any>({});
  const [symbol, setSymbol] = useState(baseCurrency);

  useEffect(() => {
    setReceivedAmount(
      Number(formDetails.amount) > fee ? Number(formDetails.amount) - fee : 0
    );
  }, [fee, formDetails.amount]);

  useEffect(() => {
    setWallet(wallets[0]);
    setSymbol;
  }, [wallets, symbol]);

  useEffect(() => {
    async function fetchAssetFees() {
      try {
        const res = await api.get("/v1/wallets/asset_fees");
        console.log(res);

        if (res && res.data && res.data.data) {
          const assetFee = res.data.data.find(
            (f: any) => f.name?.toLowerCase() === wallet.assetType.toLowerCase()
          )?.fee;
          setFee(assetFee || 0);
        }
      } catch (err) {
        console.log("Error fetching asset fees:", err);
      }
    }

    fetchAssetFees();
  }, [wallet.assetType]);

  const submitForm = async (e: FormEvent) => {
    e.preventDefault();
    setLoading(true);

    if (!formDetails.amount || !formDetails.address || !otp) {
      toast.error("Fill in missing properties");
      setLoading(false);
      return;
    }

    if (formDetails.amount < 0) {
      toast.error("Cannot withdraw a negative value");
      setLoading(false);
      return;
    }

    if (formDetails.address.length < 20) {
      toast.error("Invalid wallet address");
      setLoading(false);
      return;
    }

    api
      // .post("/v1/wallets/crypto-transfer", {
      .post("/v2/transfers/withdraw", {
        amount: Number(formDetails.amount),
        // walletID: wallet.id,
        // fromAddress: wallet.address,
        toAddress: formDetails.address,
        assetType: "CRYPTO",
        // network: wallet.network,
        currency: wallet.symbol,
        otp,
      })
      .then((res) => {
        console.log(res);
        if (res?.status?.toString().startsWith("2")) {
          // window.location.reload();
          // setModal(<PendingModal />);
          setModal(<SuccessModal message="Transaction successful" />);
        } else {
          toast.error(res.data.message);
        }
      })
      .catch((err) => {
        console.log("ërror", err);
        toast.error(err.message);
      })
      .finally(() => {
        console.log("finally");
        setLoading(false);
      });
  };

  const sendWithrawalOtp = async (e: FormEvent) => {
    e.preventDefault();
    setLoading(true);

    if (!formDetails.amount || !formDetails.address) {
      toast.error("Fill in missing properties");
      setLoading(false);
      return;
    }

    if (formDetails.amount < 0) {
      toast.error("Cannot withdraw a negative value");
      setLoading(false);
      return;
    }

    if (formDetails.address.length < 20) {
      toast.error("Invalid wallet address");
      setLoading(false);
      return;
    }

    api
      .post("/v1/wallets/send/withdrawal-otp", {
        amount: Number(formDetails.amount),
        walletID: wallet.id,
        address: formDetails.address,
      })
      .then((res) => {
        console.log(res);
        if (res?.status?.toString().startsWith("2")) {
          setisOtpAvailable(true);
        } else {
          toast.error(res.data.message);
        }
      })
      .catch((err) => {
        console.log("ërror", err);
        toast.error(err.message);
      })
      .finally(() => {
        console.log("finally");
        setLoading(false);
      });
  };

  const options = wallets.map((wallet: any) => ({
    value: wallet.network,
    label: wallet.network,
  }));

  const walletToDisplayBalance = wallets;

  console.log(walletToDisplayBalance);

  return (
    <>
      <ToastContainer />
      <div className="bg-white rounded-md h-fit max-w-80 md:max-w-96 p-4 grow">
        <div className="flex justify-between items-center">
          <h3 className="font-semibold">Withdraw</h3>
          <button onClick={() => setModal(null)}>
            <XMarkIcon className="w-6 h-6" />
          </button>
        </div>

        <form
          onSubmit={submitForm}
          className="bg-white rounded-md h-fit grow pb-6"
        >
          <p className="font-bold text-md my-5 mx-auto text-right">
            Balance: {walletToDisplayBalance[0]?.balance}
          </p>

          <div className="flex items-center relative">
            <label className="w-max absolute top-4 left-4 text-xs font-light text-zinc-400 z-10">
              Amount
            </label>
            <input
              name="amount"
              type="text"
              value={formDetails.amount || ""}
              autoComplete="off"
              onChange={(e) => {
                const value = e.target.value;
                if (/^\d*\.?\d*$/.test(value)) {
                  setFormDetails({
                    ...formDetails,
                    [e.target.name]: value === "" ? "" : value,
                  });
                }
              }}
              className="mt-1 px-4 pt-6 pb-1 pr-24 bg-white border shadow-sm border-zinc-300 placeholder-zinc-400 focus:outline-none focus:border-primary focus:ring-primary text-zinc-800 block w-full rounded-md focus:ring-1"
              placeholder={"0.00"}
            />
            <button
              type="button"
              className="flex items-center gap-x-2 rounded-full absolute right-3 font-medium px-4 py-2"
            >
              <CryptoIcon assetType={symbol as iWalletSymbol} />
              {getTokenName(symbol).symbol}
              <ChevronDownIcon width={18} height={18} />
            </button>
          </div>

          <div className="mx-auto w-[260px] mt-1">
            <p className="text-xs font-semibold my-3">Asset Fee: {fee}</p>
          </div>

          {symbol === "USDT" ? (
            <div className="my-3">
              <Select
                options={options}
                placeholder="Select Network"
                isSearchable={true}
                onChange={(e: any) => {
                  setWallet(
                    wallets.find((wallet: any) => wallet.network === e.value)
                  );
                }}
                styles={{
                  control: (baseStyles: any) => ({
                    ...baseStyles,
                    border: "1px solid #CACACA",
                    boxShadow: "none",
                    ":focus-within": {
                      border: "1px solid #7928FF",
                    },
                    padding: "4px 2px 4px 2px",
                    fontSize: "14px",
                    marginTop: "8px",
                  }),
                }}
              />
            </div>
          ) : null}

          <div className="flex items-center relative">
            <label className="w-max absolute top-4 left-4 text-xs font-light text-zinc-400 z-10">
              Wallet Address
            </label>
            <input
              name="address"
              autoComplete="off"
              type="text"
              value={formDetails.address || ""}
              onChange={(e) =>
                setFormDetails({
                  ...formDetails,
                  [e.target.name]: e.target.value,
                })
              }
              className="mt-1 px-4 pt-6 pb-1 pr-24 bg-white border shadow-sm border-zinc-300 placeholder-zinc-400 focus:outline-none focus:border-primary focus:ring-primary text-zinc-800 block w-full rounded-md focus:ring-1"
            />
          </div>

          {/* Add the CELO network info message */}
          <div className="mx-auto w-[260px] mt-2">
            <p className="text-xs text-gray-600 flex items-center gap-1">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="currentColor"
                className="w-4 h-4 text-blue-500"
              >
                <path
                  fillRule="evenodd"
                  d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm8.706-1.442c1.146-.573 2.437.463 2.126 1.706l-.709 2.836.042-.02a.75.75 0 01.67 1.34l-.04.022c-1.147.573-2.438-.463-2.127-1.706l.71-2.836-.042.02a.75.75 0 11-.671-1.34l.041-.022zM12 9a.75.75 0 100-********* 0 000 1.5z"
                  clipRule="evenodd"
                />
              </svg>
              Withdrawing to Binance Network
            </p>
          </div>

          {isOtpAvailable ? (
            <div className="flex flex-col border mt-1 rounded p-2 w-[260px] mx-auto">
              <label className="text-[#999999] text-xs font-light">OTP</label>
              <input
                name="otp"
                autoComplete="off"
                type="text"
                value={otp}
                onChange={(e) => setOtp(e.target.value)}
                className="w-full bg-transparent outline-none text-sm text-text-[#999999]"
              />
            </div>
          ) : null}

          {!isOtpAvailable ? (
            <button
              className="bg-appPurple text-white rounded py-2 px-4 mt-2.5 text-sm w-[260px] block mx-auto"
              onClick={sendWithrawalOtp}
            >
              {loading ? <LoadingSpinner className="mx-auto" /> : "Proceed"}
            </button>
          ) : null}

          {isOtpAvailable ? (
            <button className="bg-appPurple text-white rounded py-2 px-4 mt-2.5 text-sm w-[260px] block mx-auto">
              {loading ? <LoadingSpinner className="mx-auto" /> : "Submit"}
            </button>
          ) : null}

          {/* <button className="bg-appPurple text-white rounded py-2 px-4 mt-2.5 text-sm w-[260px] block mx-auto">
            {loading ? <LoadingSpinner className="mx-auto" /> : "Proceed"}
          </button> */}
        </form>
      </div>
    </>
  );
}

export default WithdrawCryptoModal;
