"use client";

// Third-party imports
import { zodResolver } from "@hookform/resolvers/zod";
import { Circle<PERSON>he<PERSON>, Loader2, Refresh<PERSON>w } from "lucide-react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import axios from "axios";

// UI Components
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";
import { Switch } from "@/components/ui/switch";
import { toast } from "sonner";

// Utilities
import { api, CONSTANTS, getToken } from "@/app/utils";
import { useEffect, useState } from "react";
import { Skeleton } from "@/components/ui/skeleton";

function SwitchForm() {
  const [isManual, setIsManual] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState(false);

  const FormSchema = z.object({
    isManual: z.boolean(),
  });

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      isManual: false,
    },
  });

  async function onSubmit(data: z.infer<typeof FormSchema>) {
    console.log(data);
    try {
      setIsLoading(true);
      await axios.post(
        `${CONSTANTS.SERVER_URL}/v1/admin/set-admin-rates-switch`,
        {
          isManual: data.isManual,
        },
        {
          headers: {
            Authorization: "Bearer " + getToken(),
          },
        }
      );
      setIsManual(data.isManual);
      toast.success("Rate system setting updated successfully");
    } catch (error) {
      console.error(error);
      toast.error("Failed to update rate system setting");
    } finally {
      setIsLoading(false);
    }
  }

  useEffect(() => {
    const fetchRatesState = async () => {
      try {
        const res = await axios.get(
          `${CONSTANTS.SERVER_URL}/v1/admin/get-admin-rates-state`,
          {
            headers: {
              Authorization: "Bearer " + getToken(),
            },
          }
        );
        setIsManual(res.data.isManual);
        form.setValue("isManual", res.data.isManual);
        console.log(form);
      } catch (error) {
        console.error(error);
        toast.error("Failed to fetch rate system state");
      }
    };

    fetchRatesState();
  }, [form]);

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="lg:max-w-lg space-y-3"
      >
        <div>
          <h3 className="mb-4 text-lg font-medium">Rate System</h3>
          <div className="space-y-4">
            <FormField
              control={form.control}
              name="isManual"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                  <div className="space-y-0.5">
                    <FormLabel>Manual Rate System</FormLabel>
                    <FormDescription>
                      Allows admin set rate manually.
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={(checked) => {
                        field.onChange(checked);
                        form.handleSubmit(onSubmit)();
                      }}
                      disabled={isLoading}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
        </div>
      </form>
    </Form>
  );
}

function ManualRateModal() {
  const [open, setOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [rates, setRates] = useState<{ sellRate: number } | undefined>();
  const [isLoadingRates, setIsLoadingRates] = useState(false);
  const FormSchema = z.object({
    rate: z.coerce.number().positive("Rate must be positive"),
  });
  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      rate: 0,
    },
  });
  const onSubmit = async (data: z.infer<typeof FormSchema>) => {
    try {
      setIsLoading(true);
      const res = await axios.post(
        `${CONSTANTS.SERVER_URL}/v1/admin/set-admin-manual-rates`,
        {
          sellRate: data.rate,
          isManual: true,
        },
        {
          headers: {
            Authorization: "Bearer " + getToken(),
          },
        }
      );
      toast(
        <span className="text-md">
          Rate has been set to{" "}
          <span className="font-bold text-purple-600">{data.rate}</span>
        </span>,
        {
          className: "max-w-[350px]",
          icon: <CircleCheck className="h-4 w-4" strokeWidth={"3px"} />,
        }
      );
      setOpen(false);
    } catch (error) {
      console.error(error);
      toast.error("Failed to set rate");
    } finally {
      setIsLoading(false);
      form.reset();
    }
  };
  const fetchRates = async () => {
    setIsLoadingRates(true);

    try {
      const res = await api.get("/get-conversion-rates");
      console.log(res.data);
      setRates(res.data);
    } catch (err) {
      console.error("Error fetching rates:", err);
    } finally {
      setIsLoadingRates(false);
    }
  };

  useEffect(() => {
    if (open) {
      fetchRates();
    }
  }, [open]);

  const handleRefreshRates = () => {
    fetchRates();
  };
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          className="w-md text-white bg-purple-600 hover:bg-purple-700 active:bg-purple-800"
        >
          Set Rate
        </Button>
      </DialogTrigger>
      <DialogContent className="lg:max-w-md">
        <div className="p-4 space-y-6 font-circularStd">
          <div className="text-center">
            <h2 className="text-lg font-medium text-purple-600">Set Rate</h2>
            <div className="h-0.5 w-[60%] bg-purple-600 mx-auto mt-1"></div>
          </div>

          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(onSubmit)}
              className="p-4 space-y-6"
            >
              <FormField
                control={form.control}
                name="rate"
                render={({ field }) => (
                  <FormItem className="bg-gray-50 rounded-lg px-4 py-2 border-[#CACACA] border-2">
                    <FormLabel className="text-sm text-left text-gray-500 block mb-2">
                      New rate
                    </FormLabel>
                    <FormControl>
                      <input
                        type="number"
                        className="bg-transparent text-lg font-medium outline-none w-full [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                        {...field}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <div className="text-sm text-gray-500 flex gap-2 justify-center">
                {isLoadingRates ? (
                  <>
                    <p className="font-bold flex gap-2 items-center">
                      Current rate:{" "}
                      <Skeleton className="inline-block w-16 h-4" />
                    </p>
                  </>
                ) : (
                  <>
                    <p className="font-bold">
                      Current rate:{" "}
                      <span className="font-normal">{rates?.sellRate}</span>
                    </p>
                  </>
                )}
              </div>
              <Button
                type="button"
                variant="outline"
                onClick={handleRefreshRates}
                disabled={isLoadingRates}
                className="mx-auto mt-32 flex"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                <span>Refresh rates</span>
              </Button>
              <Button
                type="submit"
                disabled={isLoading}
                className="w-full bg-purple-600 hover:bg-purple-700 active:bg-purple-800"
              >
                {isLoading ? (
                  <div className="flex items-center gap-2">
                    <Loader2 strokeWidth="3px" className="animate-spin" />
                    <p>Setting Rate...</p>
                  </div>
                ) : (
                  "Set Rate"
                )}
              </Button>
            </form>
          </Form>
        </div>
      </DialogContent>
    </Dialog>
  );
}

export default function Page() {
  
  return (
    <section className="p-6 space-y-8 font-circularStd">
      <div className="flex items-center gap-2 text-muted-foreground">
        <h2 className="text-xl font-bold">Settings</h2>
      </div>

      <div className="space-y-10">
        <SwitchForm />
        <hr className="my-6 border-t border-gray-300" />
        <div>
          <h3 className="mb-2 text-lg font-semibold">Manual Rate Control</h3>
          <p className="text-muted-foreground mb-4">
            Set and adjust exchange rates manually when automatic rate system is
            disabled.
          </p>
          <ManualRateModal />
        </div>
      </div>

      <div>
        <h3>Switch Fiat Provider</h3>
        <p className="text-muted-foreground mb-4">
          Set and adjust exchange rates manually when automatic rate system is
          disabled.
        </p>
        <Select>
          <SelectTrigger>
            <SelectValue placeholder="Select Fiat Provider" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="novac">NOVAC</SelectItem>
            <SelectItem value="maplerad">MAPLERAD</SelectItem>
            <SelectItem value="swerve">SWERVE</SelectItem>
          </SelectContent>

        </Select>

      </div>
    </section>
  );
}
