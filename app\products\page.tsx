"use client";

import { useEffect, useState } from "react";
import axios from "axios";
import { CONSTANTS, getToken } from "../utils";
import Link from "next/link";
import Image from "next/image";
import { Reveal } from "@/components/animations/animations";
import { ScaleButton } from "@/components/animations/animations";
import Nav from "@/components/Nav";
import Faq from "@/components/homepage/Faq";
import Footer from "@/components/Footer";

export default function Page() {
  const [user, setUser] = useState(null);

  useEffect(() => {
    async function fetchAuth() {
      try {
        const res = await axios.get(CONSTANTS.SERVER_URL + "/v1/auth", {
          headers: { Authorization: "Bearer " + getToken() },
        });
        if (res.status === 403) return;
        else setUser(res.data.data);
        console.log(res.data.data);
      } catch (err: any) {
        if (err.response) {
          if (err.response.status === 403) {
            // ------ Not logged in
          }
        }
        console.log(err);
      }
    }

    fetchAuth();
  }, []);
  return (
    <div className="font-circularStd">
      <Nav />
      <header>
        <section className="bg-appWhite h-screen lg:h-[calc(100vh-100px)] bg-[url(/images/hero-bg.jpg)] bg-cover bg-top bg-no-repeat">
          <div className="w-full px-4 h-[90%] py-28">
            <h1 className="text-4xl font-bold text-center mb-10">
              Flincap Products
            </h1>
            <div className="lg:block text-center flex justify-center items-center">
              <Reveal delay={0.1}>
                <div className="relative inline-block w-full max-w-4xl after:content-[''] after:absolute after:top-[0.3rem] after:left-[0.3rem] after:w-full after:h-full after:rounded-xl after:bg-purple-600">
                  <div className="rounded-xl overflow-hidden border-[3px] border-black border-solid relative z-10">
                    <Image
                      src="/images/illustrations/hexagon.jpg"
                      alt="Hexagon"
                      width={540}
                      height={340}
                      className="w-full h-40 md:h-52 object-cover block"
                    />
                  </div>
                </div>
                <p className="text-center text-md mx-auto mt-8 md:text-lg md:max-w-[70%]">
                  Flincap offers a suite of interconnected products that
                  together form a comprehensive borderless payments stack.
                  Whether you need a developer API for stablecoin and local
                  African currency payments or an OTC desk for large
                  transactions, our products have you covered:
                </p>
              </Reveal>
            </div>
          </div>
        </section>
      </header>

      <section className="py-16 px-4 bg-gray-50 relative overflow-hidden">
        {/* Radial blur effect on the right */}
        <div className="absolute top-0 left-0 w-96 h-96 bg-gradient-radial from-blue-400 via-blue-300/20 to-transparent blur-3xl pointer-events-none"></div>

        <div className="max-w-7xl mx-auto relative z-10">
          <div className="flex flex-col lg:flex-row items-center gap-12">
            {/* Left side - Illustration */}
            <div className="flex-1 flex justify-center">
              <div className="relative">
                <Image
                  src="/images/illustrations/paymentapi.svg"
                  alt="Stablecoin Payments API Illustration"
                  width={500}
                  height={400}
                  className="w-full max-w-lg object-contain"
                />
              </div>
            </div>

            {/* Right side - Content */}
            <div className="flex-1">
              <div className="bg-blue-400 rounded-2xl p-8 border-4 border-black shadow-lg">
                <h2 className="text-2xl font-bold text-black mb-4">
                  Stablecoin Payments API
                </h2>

                <p className="text-black mb-6 text-sm leading-relaxed">
                  Our Stablecoin API is a developer-friendly solution that
                  enables seamless integration of stablecoin infrastructure into
                  your applications. With this RESTful API and webhooks, you
                  can:
                </p>

                <div className="space-y-4 mb-8">
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-black rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-black text-sm leading-relaxed">
                      <strong>Send and Receive Payments:</strong>{" "}
                      Programmatically transfer USDC/USDT to and from Flincap,
                      with automatic conversion to local currencies for payouts.
                      For example, you can send a payment from your USD
                      stablecoin balance and have a vendor in Nigeria receive it
                      as NGN in their bank account within minutes.
                    </p>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-black rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-black text-sm leading-relaxed">
                      <strong>Multi-Chain Support:</strong> Interact with
                      stablecoins across multiple blockchains without worrying
                      about the technical complexity. Our API abstracts the
                      details, so whether it&apos;s on Ethereum, Tron, or other
                      supported networks, you get a unified interface.
                    </p>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-black rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-black text-sm leading-relaxed">
                      <strong>Collections (Pay-ins):</strong> Generate virtual
                      account details or payment links to collect local payments
                      in various African countries. Funds collected via bank
                      transfer or mobile money are automatically converted to
                      stablecoins or credited to your Flincap balance in USD.
                      This is ideal for businesses accepting customer payments
                      or funding their Flincap account from local markets.
                    </p>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-black rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-black text-sm leading-relaxed">
                      <strong>Payouts (Pay-outs):</strong> Initiate payouts to
                      bank accounts, mobile wallets, or cash pickup points
                      across our network. The API takes in your stablecoin or
                      USD balance and handles last-mile delivery in the
                      recipient&apos;s local currency. Flincap orchestrates the
                      conversion and transfer via our compliant local partners,
                      so your recipients get paid quickly with no hassle.
                    </p>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-black rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-black text-sm leading-relaxed">
                      <strong>Real-Time Monitoring & Control:</strong> Use our
                      dashboard or API endpoints to monitor transaction status
                      in real time. Receive callbacks for settlement
                      confirmations. Set spending limits, rules, and automate
                      treasury functions. Flincap&apos;s API is designed with
                      enterprise-grade controls to fit your business workflows.
                    </p>
                  </div>
                </div>

                <p className="text-black text-sm leading-relaxed mb-6">
                  <strong>Why it matters:</strong> Our API-first approach means
                  you can build custom payment flows on top of Flincap easily.
                  Rather than spending months integrating various banks and
                  crypto exchanges, a single integration with Flincap gives you
                  access to a network of stablecoin liquidity and local payment
                  rails. Detailed documentation and a sandbox environment are
                  available to get you started quick!
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="py-16 px-4 bg-gray-50 relative overflow-hidden">
        <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-radial from-[#01D797] via-blue-300/20 to-transparent blur-3xl pointer-events-none"></div>
        <div className="max-w-7xl mx-auto relative z-10">
          <div className="flex flex-col lg:flex-row items-center gap-12">
            {/* Right side - Content */}
            <div className="flex-1">
              <div className="bg-[#01D797] rounded-2xl p-8 border-4 border-black shadow-lg">
                <h2 className="text-2xl font-bold text-black mb-4">OTC Desk</h2>

                <p className="text-black mb-6 text-sm leading-relaxed">
                  For clients who need to handle large-volume transactions or
                  treasury conversions, Flincap&apos;s OTC (Over-The-Counter)
                  Desk provides a personalized, high-liquidity service. The OTC
                  Desk is perfect for scenarios like:
                </p>

                <div className="space-y-4 mb-8">
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-black rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-black text-sm leading-relaxed">
                      <strong>Bulk Stablecoin Liquidity:</strong> Converting
                      large sums of fiat to stablecoins (or vice versa) beyond
                      the limits of automated exchange channels. Our traders
                      provide competitive quotes for high-volume trades, tapping
                      into deep liquidity pools to minimize slippage.
                    </p>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-black rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-black text-sm leading-relaxed">
                      <strong>Treasury Management Moves:</strong> If your
                      company is moving significant funds into USD stablecoins
                      for hedging or transferring earnings out of an African
                      country, the OTC service ensures you get the best rates
                      and guidance. We support USDT, USDC and can facilitate
                      other major digital assets on request, across major
                      chains.
                    </p>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-black rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-black text-sm leading-relaxed">
                      <strong>Collections (Pay-ins):</strong> Generate virtual
                      account details or payment links to collect local payments
                      in various African countries. Funds collected via bank
                      transfer or mobile money are automatically converted to
                      stablecoins or credited to your Flincap balance in USD.
                      This is ideal for businesses accepting customer payments
                      or funding their Flincap account from local markets.
                    </p>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-black rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-black text-sm leading-relaxed">
                      <strong>Personalized Service:</strong> Unlike an open
                      exchange, our OTC gives you direct access to
                      Flincap&apos;s trading desk for one-on-one service.
                      Execute large trades with confidentiality and expert
                      assistance. Our team ensures compliance checks are smooth
                      even for large transactions, and that settlement is
                      handled end-to-end (including arranging fiat bank
                      transfers if needed).
                    </p>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-black rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-black text-sm leading-relaxed">
                      <strong>Flexible Settlement:</strong> The OTC Desk can
                      settle trades in multiple ways – on-platform Flincap
                      balances, on-chain transfers to your own crypto wallet, or
                      wire transfers for fiat. This flexibility is crucial for
                      enterprises dealing with complex treasury operations.
                    </p>
                  </div>
                </div>

                <p className="text-black text-sm leading-relaxed mb-6">
                  By using Flincap&apos;s OTC Desk, businesses avoid moving
                  markets or facing liquidity bottlenecks that can occur on
                  public exchanges. You get the assurance of deep liquidity,
                  quick settlement, and competitive pricing in every large
                  transaction. (To inquire about OTC trades, please contact our
                  team for a custom quote.)
                </p>
              </div>
            </div>

            {/* Left side - Illustration */}
            <div className="flex-1 flex justify-center">
              <div className="relative">
                <Image
                  src="/images/illustrations/otcdesk.svg"
                  alt="Stablecoin Payments API Illustration"
                  width={500}
                  height={400}
                  className="w-full max-w-md object-contain"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="py-16 px-4 bg-gray-50 relative overflow-hidden">
        <div className="absolute top-0 left-0 w-96 h-96 bg-gradient-radial from-[#7928FF] via-blue-300/20 to-transparent blur-3xl pointer-events-none"></div>
        <div className="max-w-7xl mx-auto relative z-10">
          <div className="flex flex-col lg:flex-row items-center gap-12">
            {/* Right side - Content */}
            <div className="flex-1 flex justify-center">
              <div className="relative">
                <Image
                  src="/images/illustrations/localpayouts.png"
                  alt="Stablecoin Payments API Illustration"
                  width={500}
                  height={400}
                  className="w-full max-w-md object-contain"
                />
              </div>
            </div>

            {/* Left side - Illustration */}
            <div className="flex-1">
              <div className="bg-[#7928FF] rounded-2xl p-8 border-4 border-black shadow-lg">
                <h2 className="text-2xl font-bold text-white mb-4">
                  Local Payouts
                </h2>

                <p className="text-white mb-6 text-sm leading-relaxed">
                  Flincap Payout solutions enable you to disburse funds in local
                  currencies across Africa, while funding those payouts from a
                  single stablecoin liquidity source. It&apos;s a bridge between
                  the crypto world and local banking systems:
                </p>

                <div className="space-y-4 mb-8">
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-white rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-white text-sm leading-relaxed">
                      <strong>Multi-Country Payout Network:</strong> Currently
                      supporting payouts to banks and mobile money in Nigeria,
                      Ghana, Kenya, South Africa (with more on the way). Send
                      one bulk instruction or single payout via our
                      dashboard/API, and Flincap will deliver the money to your
                      beneficiary&apos;s bank account or mobile wallet in their
                      local currency.
                    </p>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-white rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-white text-sm leading-relaxed">
                      <strong>Real-Time or Same-Day Settlement:</strong> Thanks
                      to our stablecoin infrastructure, your funds move at the
                      speed of crypto to each target country, where we then
                      utilize local faster payment networks. Recipients can
                      often access the money within minutes of your instruction,
                      or at worst within the same business day – a radical
                      improvement over traditional wire transfers that can take
                      days.
                    </p>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-white rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-white text-sm leading-relaxed">
                      <strong>Compliance and Transparency:</strong> Every payout
                      complies with local regulations. Flincap conducts KYC on
                      senders and required checks on recipients as needed,
                      ensuring that even though stablecoins are used behind the
                      scenes, the last-mile payout is fully regulated. You can
                      download reports of all transactions for your compliance
                      and accounting
                    </p>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-white rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-white text-sm leading-relaxed">
                      <strong>Use Case – Payroll & Vendor Payments:</strong>{" "}
                      Simplify paying a distributed workforce or multiple
                      vendors. For example, a pan-African company can use
                      Flincap to pay employees in 5 different countries from a
                      single USD stablecoin pool, with each employee receiving
                      local currency without worrying about exchange rates. No
                      more dealing with each country&apos;s banking bureaucracy
                      – one integration handles it.
                    </p>
                  </div>
                </div>

                <p className="text-white text-sm leading-relaxed mb-6">
                  Our Payout product is what turns Flincap into a true
                  global-to-local payments engine. It allows crypto value to
                  seamlessly integrate with real economies, making it highly
                  practical for business operations.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="py-16 px-4 bg-gray-50 relative overflow-hidden">
        <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-radial from-[#E0C602] via-blue-300/20 to-transparent blur-3xl pointer-events-none"></div>
        <div className="max-w-7xl mx-auto relative z-10">
          <div className="flex flex-col lg:flex-row items-center gap-12">
            {/* Right side - Content */}
            <div className="flex-1">
              <div className="bg-[#E0C602] rounded-2xl p-8 border-4 border-black shadow-lg">
                <h2 className="text-2xl font-bold text-black mb-4">
                  Local Collections (Pay-in)
                </h2>

                <p className="text-black mb-6 text-sm leading-relaxed">
                  The counterpart to payouts, Flincap Pay-in enables businesses
                  to collect local currency payments from customers or partners,
                  which are then settled to you in stablecoins or in your
                  Flincap USD wallet balance. Key features:
                </p>

                <div className="space-y-4 mb-8">
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-black rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-black text-sm leading-relaxed">
                      <strong>Virtual Accounts & Payment Methods:</strong> Issue
                      local bank account numbers or mobile money references in
                      supported countries for your business. When someone pays
                      that account (e.g. a customer in Kenya sends KES to the
                      local account), Flincap automatically credits your balance
                      with the equivalent stablecoin at the current rate. We
                      handle the FX conversion and crypto issuance behind the
                      scenes.
                    </p>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-black rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-black text-sm leading-relaxed">
                      <strong>Hosted Checkout and Payment Links:</strong> If you
                      don’t want to integrate APIs, you can use Flincap’s hosted
                      payment page to accept payments. Simply generate a payment
                      link for an invoice or embed our checkout widget in your
                      app. Your payers can then use their preferred local method
                      – bank transfer, mobile money, card – and you’ll receive
                      the value in a stablecoin or in USD credit. This opens
                      your business to customers who want to pay in local
                      currency while you maintain revenues in a stable USD
                      value.
                    </p>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-black rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-black text-sm leading-relaxed">
                      <strong>Named accounts:</strong> Get dedicated, name bank
                      account numbers for easy identification and trust. When
                      customers pay into your named account, the funds are
                      instantly credited to your Flincap balance.
                    </p>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-black rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-black text-sm leading-relaxed">
                      <strong>Reconciliation Tools:</strong> Each pay-in
                      transaction is tagged with customer references and
                      metadata you can attach (invoices IDs, customer IDs,
                      etc.), making reconciliation a breeze. Our dashboard
                      provides filtering and reporting so you can match incoming
                      payments with orders or invoices quickly.
                    </p>
                  </div>
                </div>

                <p className="text-black text-sm leading-relaxed mb-6">
                  Flincap’s Pay-in product essentially gives you a local
                  presence in each market without a local entity. Your customers
                  pay locally, but you receive value globally. By closing the
                  loop with our payout product, you can both collect and
                  disburse funds locally while centralizing treasury in
                  stablecoins – achieving true financial integration across
                  borders.
                </p>
              </div>
            </div>

            {/* Left side - Illustration */}
            <div className="flex-1 flex justify-center">
              <div className="relative">
                <Image
                  src="/images/illustrations/localpayin.svg"
                  alt="Stablecoin Payments API Illustration"
                  width={500}
                  height={400}
                  className="w-full max-w-md object-contain"
                />
              </div>
            </div>
          </div>
        </div>
      </section>
      <Faq />
      <Footer />
    </div>
  );
}
