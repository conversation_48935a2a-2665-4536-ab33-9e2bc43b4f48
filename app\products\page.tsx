"use client";

import { useEffect, useState } from "react";
import axios from "axios";
import { CONSTANTS, getToken } from "../utils";
import Link from "next/link";
import Image from "next/image";
import { Reveal } from "@/components/animations/animations";
import { ScaleButton } from "@/components/animations/animations";
import Nav from "@/components/Nav";

export default function Page() {
  const [user, setUser] = useState(null);

  useEffect(() => {
    async function fetchAuth() {
      try {
        const res = await axios.get(CONSTANTS.SERVER_URL + "/v1/auth", {
          headers: { Authorization: "Bearer " + getToken() },
        });
        if (res.status === 403) return;
        else setUser(res.data.data);
        console.log(res.data.data);
      } catch (err: any) {
        if (err.response) {
          if (err.response.status === 403) {
            // ------ Not logged in
          }
        }
        console.log(err);
      }
    }

    fetchAuth();
  }, []);
  return (
    <div>
      <Nav />
      <header>
        <section className="bg-appWhite h-screen lg:h-[calc(100vh-100px)] bg-[url(/images/hero-bg.jpg)] bg-cover bg-top bg-no-repeat">
          <div className="w-full px-4 h-[90%] py-28">
            <h1 className="text-4xl font-semibold text-center mb-10">
              Flincap Products
            </h1>
            <div className="lg:block text-center flex justify-center items-center">
              <Reveal delay={0.1}>
                <div className="relative inline-block w-full max-w-4xl after:content-[''] after:absolute after:top-[0.3rem] after:left-[0.3rem] after:w-full after:h-full after:rounded-xl after:bg-purple-600">
                  <div className="rounded-xl overflow-hidden border-[3px] border-black border-solid relative z-10">
                    <Image
                      src="/images/illustrations/hexagon.jpg"
                      alt="Hexagon"
                      width={540}
                      height={340}
                      className="w-full h-40 md:h-52 object-cover block"
                    />
                  </div>
                </div>
                <p className="text-center text-md mx-auto mt-8 md:text-lg md:max-w-[70%]">
                  Flincap offers a suite of interconnected products that
                  together form a comprehensive borderless payments stack.
                  Whether you need a developer API for stablecoin and local
                  African currency payments or an OTC desk for large
                  transactions, our products have you covered:
                </p>
              </Reveal>
            </div>
          </div>
        </section>
      </header>

      <section className="py-16 px-4 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-col lg:flex-row items-center gap-12">
            {/* Left side - Illustration */}
            <div className="flex-1 flex justify-center">
              <div className="relative">
                <Image
                  src="/images/illustrations/paymentapi.svg"
                  alt="Stablecoin Payments API Illustration"
                  width={500}
                  height={400}
                  className="w-full max-w-lg object-contain"
                />
              </div>
            </div>

            {/* Right side - Content */}
            <div className="flex-1">
              <div className="bg-blue-400 rounded-2xl p-8 border-4 border-black shadow-lg">
                <h2 className="text-2xl font-bold text-black mb-4">
                  Stablecoin Payments API
                </h2>

                <p className="text-black mb-6 text-sm leading-relaxed">
                  Our Stablecoin API is a developer-friendly solution that
                  enables seamless integration of stablecoin infrastructure into
                  your applications. With this RESTful API and webhooks, you
                  can:
                </p>

                <div className="space-y-4 mb-8">
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-black rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-black text-sm leading-relaxed">
                      <strong>Send and Receive Payments:</strong>{" "}
                      Programmatically transfer USDC/USDT to and from Flincap,
                      with automatic conversion to local currencies for payouts.
                      For example, you can send a payment from your USD
                      stablecoin balance and have a vendor in Nigeria receive it
                      as NGN in their bank account within minutes.
                    </p>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-black rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-black text-sm leading-relaxed">
                      <strong>Multi-Chain Support:</strong> Interact with
                      stablecoins across multiple blockchains without worrying
                      about the technical complexity. Our API abstracts the
                      details, so whether it's on Ethereum, Tron, or other
                      supported networks, you get a unified interface.
                    </p>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-black rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-black text-sm leading-relaxed">
                      <strong>Collections (Pay-ins):</strong> Generate virtual
                      account details or payment links to collect local payments
                      in various African countries. Funds collected via bank
                      transfer or mobile money are automatically converted to
                      stablecoins or credited to your Flincap balance in USD.
                      This is ideal for businesses accepting customer payments
                      or funding their Flincap account from local markets.
                    </p>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-black rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-black text-sm leading-relaxed">
                      <strong>Payouts (Pay-outs):</strong> Initiate payouts to
                      bank accounts, mobile wallets, or cash pickup points
                      across our network. The API takes in your stablecoin or
                      USD balance and handles last-mile delivery in the
                      recipient's local currency. Flincap orchestrates the
                      conversion and transfer via our compliant local partners,
                      so your recipients get paid quickly with no hassle.
                    </p>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-black rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-black text-sm leading-relaxed">
                      <strong>Real-Time Monitoring & Control:</strong> Use our
                      dashboard or API endpoints to monitor transaction status
                      in real time. Receive callbacks for settlement
                      confirmations. Set spending limits, rules, and automate
                      treasury functions. Flincap's API is designed with
                      enterprise-grade controls to fit your business workflows.
                    </p>
                  </div>
                </div>

                <p className="text-black text-sm leading-relaxed mb-6">
                  <strong>Why it matters:</strong> Our API-first approach means
                  you can build custom payment flows on top of Flincap easily.
                  Rather than spending months integrating various banks and
                  crypto exchanges, a single integration with Flincap gives you
                  access to a network of stablecoin liquidity and local payment
                  rails. Detailed documentation and a sandbox environment are
                  available to get you started quick!
                </p>

                <div className="flex justify-center">
                  <button className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors">
                    View API Docs
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="py-16 px-4 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-col lg:flex-row items-center gap-12">
            {/* Right side - Content */}
            <div className="flex-1">
              <div className="bg-blue-400 rounded-2xl p-8 border-4 border-black shadow-lg">
                <h2 className="text-2xl font-bold text-black mb-4">
                  Stablecoin Payments API
                </h2>

                <p className="text-black mb-6 text-sm leading-relaxed">
                  Our Stablecoin API is a developer-friendly solution that
                  enables seamless integration of stablecoin infrastructure into
                  your applications. With this RESTful API and webhooks, you
                  can:
                </p>

                <div className="space-y-4 mb-8">
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-black rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-black text-sm leading-relaxed">
                      <strong>Send and Receive Payments:</strong>{" "}
                      Programmatically transfer USDC/USDT to and from Flincap,
                      with automatic conversion to local currencies for payouts.
                      For example, you can send a payment from your USD
                      stablecoin balance and have a vendor in Nigeria receive it
                      as NGN in their bank account within minutes.
                    </p>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-black rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-black text-sm leading-relaxed">
                      <strong>Multi-Chain Support:</strong> Interact with
                      stablecoins across multiple blockchains without worrying
                      about the technical complexity. Our API abstracts the
                      details, so whether it's on Ethereum, Tron, or other
                      supported networks, you get a unified interface.
                    </p>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-black rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-black text-sm leading-relaxed">
                      <strong>Collections (Pay-ins):</strong> Generate virtual
                      account details or payment links to collect local payments
                      in various African countries. Funds collected via bank
                      transfer or mobile money are automatically converted to
                      stablecoins or credited to your Flincap balance in USD.
                      This is ideal for businesses accepting customer payments
                      or funding their Flincap account from local markets.
                    </p>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-black rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-black text-sm leading-relaxed">
                      <strong>Payouts (Pay-outs):</strong> Initiate payouts to
                      bank accounts, mobile wallets, or cash pickup points
                      across our network. The API takes in your stablecoin or
                      USD balance and handles last-mile delivery in the
                      recipient's local currency. Flincap orchestrates the
                      conversion and transfer via our compliant local partners,
                      so your recipients get paid quickly with no hassle.
                    </p>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-black rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-black text-sm leading-relaxed">
                      <strong>Real-Time Monitoring & Control:</strong> Use our
                      dashboard or API endpoints to monitor transaction status
                      in real time. Receive callbacks for settlement
                      confirmations. Set spending limits, rules, and automate
                      treasury functions. Flincap's API is designed with
                      enterprise-grade controls to fit your business workflows.
                    </p>
                  </div>
                </div>

                <p className="text-black text-sm leading-relaxed mb-6">
                  <strong>Why it matters:</strong> Our API-first approach means
                  you can build custom payment flows on top of Flincap easily.
                  Rather than spending months integrating various banks and
                  crypto exchanges, a single integration with Flincap gives you
                  access to a network of stablecoin liquidity and local payment
                  rails. Detailed documentation and a sandbox environment are
                  available to get you started quick!
                </p>

                <div className="flex justify-center">
                  <button className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors">
                    View API Docs
                  </button>
                </div>
              </div>
            </div>

            {/* Left side - Illustration */}
            <div className="flex-1 flex justify-center">
              <div className="relative">
                <Image
                  src="/images/illustrations/paymentapi.svg"
                  alt="Stablecoin Payments API Illustration"
                  width={500}
                  height={400}
                  className="w-full max-w-md object-contain"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="py-16 px-4 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-col lg:flex-row items-center gap-12">
            {/* Right side - Content */}
            <div className="flex-1">
              <div className="bg-blue-400 rounded-2xl p-8 border-4 border-black shadow-lg">
                <h2 className="text-2xl font-bold text-black mb-4">
                  Stablecoin Payments API
                </h2>

                <p className="text-black mb-6 text-sm leading-relaxed">
                  Our Stablecoin API is a developer-friendly solution that
                  enables seamless integration of stablecoin infrastructure into
                  your applications. With this RESTful API and webhooks, you
                  can:
                </p>

                <div className="space-y-4 mb-8">
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-black rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-black text-sm leading-relaxed">
                      <strong>Send and Receive Payments:</strong>{" "}
                      Programmatically transfer USDC/USDT to and from Flincap,
                      with automatic conversion to local currencies for payouts.
                      For example, you can send a payment from your USD
                      stablecoin balance and have a vendor in Nigeria receive it
                      as NGN in their bank account within minutes.
                    </p>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-black rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-black text-sm leading-relaxed">
                      <strong>Multi-Chain Support:</strong> Interact with
                      stablecoins across multiple blockchains without worrying
                      about the technical complexity. Our API abstracts the
                      details, so whether it's on Ethereum, Tron, or other
                      supported networks, you get a unified interface.
                    </p>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-black rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-black text-sm leading-relaxed">
                      <strong>Collections (Pay-ins):</strong> Generate virtual
                      account details or payment links to collect local payments
                      in various African countries. Funds collected via bank
                      transfer or mobile money are automatically converted to
                      stablecoins or credited to your Flincap balance in USD.
                      This is ideal for businesses accepting customer payments
                      or funding their Flincap account from local markets.
                    </p>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-black rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-black text-sm leading-relaxed">
                      <strong>Payouts (Pay-outs):</strong> Initiate payouts to
                      bank accounts, mobile wallets, or cash pickup points
                      across our network. The API takes in your stablecoin or
                      USD balance and handles last-mile delivery in the
                      recipient's local currency. Flincap orchestrates the
                      conversion and transfer via our compliant local partners,
                      so your recipients get paid quickly with no hassle.
                    </p>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-black rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-black text-sm leading-relaxed">
                      <strong>Real-Time Monitoring & Control:</strong> Use our
                      dashboard or API endpoints to monitor transaction status
                      in real time. Receive callbacks for settlement
                      confirmations. Set spending limits, rules, and automate
                      treasury functions. Flincap's API is designed with
                      enterprise-grade controls to fit your business workflows.
                    </p>
                  </div>
                </div>

                <p className="text-black text-sm leading-relaxed mb-6">
                  <strong>Why it matters:</strong> Our API-first approach means
                  you can build custom payment flows on top of Flincap easily.
                  Rather than spending months integrating various banks and
                  crypto exchanges, a single integration with Flincap gives you
                  access to a network of stablecoin liquidity and local payment
                  rails. Detailed documentation and a sandbox environment are
                  available to get you started quick!
                </p>

                <div className="flex justify-center">
                  <button className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors">
                    View API Docs
                  </button>
                </div>
              </div>
            </div>

            {/* Left side - Illustration */}
            <div className="flex-1 flex justify-center">
              <div className="relative">
                <Image
                  src="/images/illustrations/paymentapi.svg"
                  alt="Stablecoin Payments API Illustration"
                  width={500}
                  height={400}
                  className="w-full max-w-md object-contain"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="py-16 px-4 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-col lg:flex-row items-center gap-12">
            {/* Right side - Content */}
            <div className="flex-1">
              <div className="bg-blue-400 rounded-2xl p-8 border-4 border-black shadow-lg">
                <h2 className="text-2xl font-bold text-black mb-4">
                  Stablecoin Payments API
                </h2>

                <p className="text-black mb-6 text-sm leading-relaxed">
                  Our Stablecoin API is a developer-friendly solution that
                  enables seamless integration of stablecoin infrastructure into
                  your applications. With this RESTful API and webhooks, you
                  can:
                </p>

                <div className="space-y-4 mb-8">
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-black rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-black text-sm leading-relaxed">
                      <strong>Send and Receive Payments:</strong>{" "}
                      Programmatically transfer USDC/USDT to and from Flincap,
                      with automatic conversion to local currencies for payouts.
                      For example, you can send a payment from your USD
                      stablecoin balance and have a vendor in Nigeria receive it
                      as NGN in their bank account within minutes.
                    </p>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-black rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-black text-sm leading-relaxed">
                      <strong>Multi-Chain Support:</strong> Interact with
                      stablecoins across multiple blockchains without worrying
                      about the technical complexity. Our API abstracts the
                      details, so whether it's on Ethereum, Tron, or other
                      supported networks, you get a unified interface.
                    </p>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-black rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-black text-sm leading-relaxed">
                      <strong>Collections (Pay-ins):</strong> Generate virtual
                      account details or payment links to collect local payments
                      in various African countries. Funds collected via bank
                      transfer or mobile money are automatically converted to
                      stablecoins or credited to your Flincap balance in USD.
                      This is ideal for businesses accepting customer payments
                      or funding their Flincap account from local markets.
                    </p>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-black rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-black text-sm leading-relaxed">
                      <strong>Payouts (Pay-outs):</strong> Initiate payouts to
                      bank accounts, mobile wallets, or cash pickup points
                      across our network. The API takes in your stablecoin or
                      USD balance and handles last-mile delivery in the
                      recipient's local currency. Flincap orchestrates the
                      conversion and transfer via our compliant local partners,
                      so your recipients get paid quickly with no hassle.
                    </p>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-black rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-black text-sm leading-relaxed">
                      <strong>Real-Time Monitoring & Control:</strong> Use our
                      dashboard or API endpoints to monitor transaction status
                      in real time. Receive callbacks for settlement
                      confirmations. Set spending limits, rules, and automate
                      treasury functions. Flincap's API is designed with
                      enterprise-grade controls to fit your business workflows.
                    </p>
                  </div>
                </div>

                <p className="text-black text-sm leading-relaxed mb-6">
                  <strong>Why it matters:</strong> Our API-first approach means
                  you can build custom payment flows on top of Flincap easily.
                  Rather than spending months integrating various banks and
                  crypto exchanges, a single integration with Flincap gives you
                  access to a network of stablecoin liquidity and local payment
                  rails. Detailed documentation and a sandbox environment are
                  available to get you started quick!
                </p>

                <div className="flex justify-center">
                  <button className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors">
                    View API Docs
                  </button>
                </div>
              </div>
            </div>

            {/* Left side - Illustration */}
            <div className="flex-1 flex justify-center">
              <div className="relative">
                <Image
                  src="/images/illustrations/paymentapi.svg"
                  alt="Stablecoin Payments API Illustration"
                  width={500}
                  height={400}
                  className="w-full max-w-md object-contain"
                />
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
