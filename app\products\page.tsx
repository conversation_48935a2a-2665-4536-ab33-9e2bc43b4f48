"use client";

import { useEffect, useState } from "react";
import axios from "axios";
import { CONSTANTS, getToken } from "../utils";
import Link from "next/link";
import Image from "next/image";
import { Reveal } from "@/components/animations/animations";
import { ScaleButton } from "@/components/animations/animations";
import Nav from "@/components/Nav";

export default function Page() {
  const [user, setUser] = useState(null);

  useEffect(() => {
    async function fetchAuth() {
      try {
        const res = await axios.get(CONSTANTS.SERVER_URL + "/v1/auth", {
          headers: { Authorization: "Bearer " + getToken() },
        });
        if (res.status === 403) return;
        else setUser(res.data.data);
        console.log(res.data.data);
      } catch (err: any) {
        if (err.response) {
          if (err.response.status === 403) {
            // ------ Not logged in
          }
        }
        console.log(err);
      }
    }

    fetchAuth();
  }, []);
  return (
    <div>
      <Nav />
      <header>
        <section className="bg-appWhite h-screen lg:h-[calc(100vh-100px)] bg-[url(/images/hero-bg.jpg)] bg-cover bg-top bg-no-repeat">
          <div className="w-full px-4 h-[90%] py-28">
            <h1 className="text-4xl font-semibold text-center mb-10">
              Flincap Products
            </h1>
            <div className="lg:block text-center flex justify-center items-center">
              <Reveal delay={0.1}>
                <div className="relative inline-block w-full max-w-4xl after:content-[''] after:absolute after:top-[0.3rem] after:left-[0.3rem] after:w-full after:h-full after:rounded-xl after:bg-purple-600">
                  <div className="rounded-xl overflow-hidden border-[3px] border-black border-solid relative z-10">
                    <Image
                      src="/images/illustrations/hexagon.jpg"
                      alt="Hexagon"
                      width={540}
                      height={340}
                      className="w-full h-40 md:h-52 object-cover block"
                    />
                  </div>
                </div>
                <p className="text-center text-md mx-auto mt-8 md:text-lg md:max-w-[70%]">
                  Flincap offers a suite of interconnected products that
                  together form a comprehensive borderless payments stack.
                  Whether you need a developer API for stablecoin and local
                  African currency payments or an OTC desk for large
                  transactions, our products have you covered:
                </p>
              </Reveal>
            </div>
          </div>
        </section>
      </header>
    </div>
  );
}
