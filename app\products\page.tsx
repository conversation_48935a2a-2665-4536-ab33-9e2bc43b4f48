"use client";

import { useEffect, useState } from "react";
import axios from "axios";
import { CONSTANTS, getToken } from "../utils";
import Link from "next/link";
import Image from "next/image";
import { Reveal } from "@/components/animations/animations";
import { ScaleButton } from "@/components/animations/animations";
import Nav from "@/components/Nav";

export default function Page() {
  const [user, setUser] = useState(null);

  useEffect(() => {
    async function fetchAuth() {
      try {
        const res = await axios.get(CONSTANTS.SERVER_URL + "/v1/auth", {
          headers: { Authorization: "Bearer " + getToken() },
        });
        if (res.status === 403) return;
        else setUser(res.data.data);
        console.log(res.data.data);
      } catch (err: any) {
        if (err.response) {
          if (err.response.status === 403) {
            // ------ Not logged in
          }
        }
        console.log(err);
      }
    }

    fetchAuth();
  }, []);
  return (
    <div>
      <Nav />
      <header>
        <section className="bg-appWhite h-screen lg:h-[calc(100vh-100px)] bg-[url(/images/hero-bg.jpg)] bg-cover bg-top bg-no-repeat">
          <div className="max-w-6xl mx-auto px-4 flex flex-col lg:flex-row justify-center lg:justify-between items-center h-[90%] py-28">
            <div className="lg:block lg:text-left text-center flex justify-center items-center flex-col">
              <Reveal delay={0.1}>
                <h1 className="text-3xl lg:text-6xl font-bold text-black mt-[-100px]">
                  Borderless. Payments. Stablecoins.
                </h1>
                <p className="my-4 lg:my-7 max-w-md lg:max-w-lg text-md lg:text-lg">
                  Send and receive payments across African currencies with ease.
                </p>
              </Reveal>
              {user ? (
                <Link
                  href="/dashboard"
                  className="bg-appPurple text-white px-6 py-2.5 rounded-md block w-fit"
                >
                  Dashboard
                </Link>
              ) : (
                <Reveal>
                  <div className="flex gap-1">
                    <ScaleButton>
                      <Link
                        href="https://forms.gle/HmaHKiMd2BJ1SGZg7"
                        className="bg-appPurple hover:bg-appPurpleDark text-white px-6 py-2.5 rounded-md block w-fit"
                      >
                        Get Started
                      </Link>
                    </ScaleButton>
                  </div>
                </Reveal>
              )}
            </div>
          </div>
        </section>
      </header>
    </div>
  );
}
