"use client";

import React, { useState, useEffect } from "react";
import { X, Shield } from "lucide-react";
import { Button } from "@/components/ui/button";
import PrivacyPolicyModal from "./modals/PrivacyPolicyModal";

export default function PrivacyBar() {
  const [isVisible, setIsVisible] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);

  useEffect(() => {
    // Check if user has already acknowledged the privacy policy
    const hasAcknowledged = localStorage.getItem("privacy-acknowledged");
    if (!hasAcknowledged) {
      // Show the bar after a short delay for better UX
      const timer = setTimeout(() => {
        setIsVisible(true);
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, []);

  const handleAccept = () => {
    localStorage.setItem("privacy-acknowledged", "true");
    setIsVisible(false);
  };

  const handleViewPolicy = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const handleDismiss = () => {
    setIsVisible(false);
    // Don't set localStorage here, so it shows again on next visit
  };

  if (!isVisible) return null;

  return (
    <>
      <div className="fixed bottom-0 left-0 right-0 z-40 bg-white border-t border-gray-200 shadow-lg">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between gap-4">
            <div className="flex items-center gap-3 flex-1">
              <Shield className="w-5 h-5 text-appPurple flex-shrink-0" />
              <div className="text-sm text-gray-700">
                <span className="font-medium">We value your privacy.</span>{" "}
                We use cookies and collect data to improve your experience and provide our services.{" "}
                <button
                  onClick={handleViewPolicy}
                  className="text-appPurple hover:underline font-medium"
                >
                  View our Privacy Policy
                </button>{" "}
                to learn more.
              </div>
            </div>
            
            <div className="flex items-center gap-2 flex-shrink-0">
              <Button
                variant="outline"
                size="sm"
                onClick={handleViewPolicy}
                className="text-appPurple border-appPurple hover:bg-appPurple hover:text-white"
              >
                Learn More
              </Button>
              <Button
                size="sm"
                onClick={handleAccept}
                className="bg-appPurple hover:bg-appPurpleDark"
              >
                Accept
              </Button>
              <button
                onClick={handleDismiss}
                className="text-gray-400 hover:text-gray-600 p-1"
                aria-label="Dismiss"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Privacy Policy Modal */}
      <PrivacyPolicyModal isOpen={isModalOpen} onClose={handleCloseModal} />
    </>
  );
}
