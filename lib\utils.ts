import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";
import html2canvas from "html2canvas";
import jsPDF from "jspdf";
import { TransactionType } from "@/types/types";
import banks from "@/banks.json";
import mapleradBanks from "@/mapleradBanks.json";
import swerveBanks from "@/swerveBanks.json";
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// // Helper function to format date and time
// export const formatDateTime = (date: Date): string => {
//   const options: Intl.DateTimeFormatOptions = {
//     year: "numeric",
//     month: "2-digit",
//     day: "2-digit",
//     hour: "2-digit",
//     minute: "2-digit",
//     hour12: true,
//   };

//   const formattedDate = new Intl.DateTimeFormat("en-US", options).format(date);
//   const timezone = "WAT"; // You can customize this if needed
//   return `${formattedDate} ${timezone}`;
// };

export const formatDateTime = (isoString: string): string => {
  const date = new Date(isoString);

  // Format the date and time into the desired format (e.g., YYYY-MM-DD HH:mm:ss)
  const year = date.getUTCFullYear();
  const month = String(date.getUTCMonth() + 1).padStart(2, "0");
  const day = String(date.getUTCDate()).padStart(2, "0");
  const hours = String(date.getUTCHours()).padStart(2, "0");
  const minutes = String(date.getUTCMinutes()).padStart(2, "0");
  const seconds = String(date.getUTCSeconds()).padStart(2, "0");
  const timezone = "UTC";

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds} ${timezone}`;
};

export const exportReceiptAsImage = async (
  transaction: Record<string, any>
) => {
  const tempDiv = document.createElement("div");
  tempDiv.style.position = "absolute";
  tempDiv.style.visibility = "visible";
  tempDiv.style.width = "500px";
  tempDiv.style.padding = "20px";
  tempDiv.style.backgroundColor = "#fff";
  tempDiv.style.border = "1px solid #000";
  tempDiv.style.fontFamily = "Helvetica, Arial, sans-serif";

  const logoUrl = "/images/cheqwa-logo.jpg";

  const transactionDate = new Date(transaction.createdAt);

  tempDiv.innerHTML = `
  <div style="display: flex; justify-content: space-between">
    <img src="${logoUrl}" alt="Logo" style="width: 100px; height: auto; margin-bottom: 10px;" />
    <p style="margin: 0;">www.cheqwa.com</p>
  </div>
  <div style="display: flex; justify-content: space-between">
    <h2 style="margin: 10px 0; font-size: 20px; font-weight: 600">Receipt</h2>
    <div style="display: flex; flex-direction: column; gap: 10px">
      <p>Amount:</p>
      <h3 style="margin: 0; font-size: 20px; font-weight: 600">NGN ${Number(
        transaction.fiatAmount
      ).toLocaleString()}</h3>
    </div>
  </div>
  <strong><h4 style="margin: 40px 0 10px; font-size: 20px">Transaction Details</h4></strong>
  <hr />
  <div style="display: flex; justify-content: space-between">
    <p><strong>Transaction Type:</strong></p>
    <p style="margin: 5px 0;"> ${
      transaction.transactionType === "WITHDRAW"
        ? "TRANSFER"
        : transaction.transactionType
    }</p>
  </div>
<div style="display: flex; justify-content: space-between">
    <p><strong>Sender:</strong></p>
    <p style="margin: 5px 0;">${transaction.user.fullName}</p>
</div>
 <div style="display: flex; justify-content: space-between">
    <p><strong>Reference:</strong></p>
    <p style="margin: 5px 0;">${transaction.id}</p>
 </div>
 <div style="display: flex; justify-content: space-between">
    <p><strong>Session ID:</strong></p>
    <p style="margin: 5px 0;">${transaction.transactionID}</p>
 </div>
 <div style="display: flex; justify-content: space-between">
    <p><strong>Account Number:</strong></p>
    <p style="margin: 5px 0;">${transaction.toAccNum}</p>
 </div>
 <div style="display: flex; justify-content: space-between">
  <p><strong>Account Name:</strong></p>
  <p style="margin: 5px 0;">${transaction.toAccName}</p>
 </div>
 <div style="display: flex; justify-content: space-between">
  <p><strong>Bank:</strong></p>
  <p style="margin: 5px 0;">${transaction.toBankName}</p>
 </div>
 <div style="display: flex; justify-content: space-between">
  <p><strong>Date:</strong></p>
  <p style="margin: 5px 0;">${new Date(
    transaction.createdAt
  ).toLocaleDateString()}</p>
 </div>
 <div style="display: flex; justify-content: space-between">
  <p><strong>Timestamp:</strong></p>
  <p style="margin: 5px 0;">${formatDateTime(transactionDate.toISOString())}</p>
 </div>
 <div style="display: flex; justify-content: space-between">
  <p><strong>Narration:</strong></p>
  <p style="margin: 5px 0;">${transaction.user.fullName}</p>
 </div>

  <div style="text-align: left; margin-top: 40px;">
    <p style="margin: 0;">For support, contact <a href="mailto:<EMAIL>"><EMAIL></a></p>
    <p style="margin: 0; font-weight: bold; color: #252ba1;">Innovative Banking for all</p>
  </div>
`;

  // Append the temporary div to the body
  document.body.appendChild(tempDiv);

  setTimeout(async () => {
    const canvas = await html2canvas(tempDiv);
    const dataURL = canvas.toDataURL("image/png");

    // Create a link element to download the image
    const link = document.createElement("a");
    link.href = dataURL;
    link.download = `transaction-${transaction.transactionID}.png`;
    link.click();

    // Remove the temporary div from the DOM
    document.body.removeChild(tempDiv);
  }, 100);
};

const fetchImageAsBase64 = async (url: string): Promise<string> => {
  const response = await fetch(url);
  const blob = await response.blob();
  return new Promise<string>((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(blob);
    reader.onloadend = () => resolve(reader.result as string);
    reader.onerror = reject;
  });
};

export const generatePDF = async (transaction: Record<string, any>) => {
  const doc = new jsPDF({
    orientation: "p",
    unit: "mm",
    format: [105, 115], // A6 size: Width: 105mm, Height: 115mm
    putOnlyUsedFonts: true,
    floatPrecision: 16,
  });

  doc.setFont("helvetica");

  const originalLogoWidth = 105;
  const originalLogoHeight = 32;
  const logoWidth = 15; // Adjust this value to change logo size
  const logoHeight = (logoWidth * originalLogoHeight) / originalLogoWidth;

  // Convert Flincap logo to base64
  const cheqwaLogoBase64 = await fetchImageAsBase64("/images/cheqwa-logo.jpg");

  // Add Flincap logo
  doc.addImage(cheqwaLogoBase64, "JPEG", 5, 5, logoWidth, logoHeight);
  // Add Flincap URL
  doc.setFontSize(8);
  doc.text("www.cheqwa.com", 100, 10, { align: "right" });

  // Add Receipt and Amount
  doc.setFontSize(12);
  doc.setFont("helvetica", "bold");
  doc.text("Receipt", 5, 18);
  doc.setFont("helvetica", "normal");
  doc.setFontSize(8);
  doc.text(`Amount`, 100, 18, { align: "right" });
  doc.setFontSize(12);
  doc.setFont("helvetica", "bold");
  doc.text(`NGN ${Number(transaction.fiatAmount).toLocaleString()}`, 100, 24, {
    align: "right",
  });
  doc.setFont("helvetica", "normal");

  // Add transaction details
  doc.setFontSize(10);
  doc.setFont("helvetica", "bold");
  doc.text("Transaction Details", 5, 32);
  doc.setFont("helvetica", "normal");
  doc.setFontSize(8);

  const detailsStart = 38;
  const lineHeight = 6.5;

  const convertToStandardDateTime = (isoString: string): string => {
    const date = new Date(isoString);

    // Format the date and time into the desired format (e.g., YYYY-MM-DD HH:mm:ss AM/PM)
    const year = date.getUTCFullYear();
    const month = String(date.getUTCMonth() + 1).padStart(2, "0");
    const day = String(date.getUTCDate()).padStart(2, "0");
    let hours = date.getUTCHours();
    const minutes = String(date.getUTCMinutes()).padStart(2, "0");
    const seconds = String(date.getUTCSeconds()).padStart(2, "0");
    const ampm = hours >= 12 ? "PM" : "AM";

    // Convert to 12-hour format
    hours = hours % 12;
    hours = hours ? hours : 12; // the hour '0' should be '12'
    const formattedHours = String(hours).padStart(2, "0");

    const timezone = "UTC";

    return `${year}-${month}-${day} ${formattedHours}:${minutes}:${seconds} ${ampm} ${timezone}`;
  };

  // Convert createdAt time to UTC using toISOString method and then format
  const transactionDate = new Date(transaction.createdAt);
  const formattedDateTime = convertToStandardDateTime(
    transactionDate.toISOString()
  );

  const details = [
    {
      label: "Transaction Type:",
      value:
        transaction.transactionType === "WITHDRAW"
          ? "TRANSFER"
          : transaction.transactionType,
    },
    { label: "Sender:", value: transaction.user.fullName },
    { label: "Reference:", value: transaction.id },
    { label: "Session ID:", value: transaction.transactionID },
    { label: "Account Number:", value: transaction.toAccNum },
    { label: "Account Name:", value: transaction.toAccName },
    { label: "Bank:", value: transaction.toBankName },
    {
      label: "Date:",
      value: transactionDate.toLocaleDateString("en-GB", { timeZone: "UTC" }),
    },
    {
      label: "Timestamp:",
      value: formattedDateTime,
    },
    {
      label: "Narration:",
      value: transaction.user.fullName,
    },
  ];

  details.forEach((detail, index) => {
    doc.text(detail.label, 5, detailsStart + lineHeight * index);
    doc.text(detail.value, 100, detailsStart + lineHeight * index, {
      align: "right",
    });
  });

  // Add support contact
  doc.setFontSize(7);
  doc.text("For support, contact <EMAIL>", 5, 100);

  // Add "Innovative Banking for all" text
  doc.setFontSize(7);
  doc.setFont("helvetica", "bold");
  doc.setTextColor(37, 42, 160); // Set color to blue
  doc.text("Innovative Banking for all", 5, 105);

  // Save the PDF
  doc.save(`transaction-${transaction.transactionID}.pdf`);
};

function escapeCsvField(field: string | null) {
  if (field == null) return "";
  const escaped = String(field).replace(/"/g, '""');
  return `"${escaped}"`;
}

export const handleExportToCSV = (transactions: TransactionType[]) => {
  const header = [
    "Asset",
    "Amount",
    "Type",
    "Customer",
    "Bank Details",
    "Date",
  ];

  const rows = transactions.map((transaction) => [
    escapeCsvField(
      transaction.assetType === "FIAT" ? "NGN" : transaction.assetType
    ),
    escapeCsvField(
      transaction.cryptoWalletId
        ? transaction.cryptoAmount.toString()
        : transaction.fiatAmount.toString()
    ),
    escapeCsvField(transaction.transactionType),
    escapeCsvField(
      transaction.cryptoWalletId
        ? transaction.transactionType === "SWAP"
          ? transaction.toAccName
          : transaction.wallet_address
        : `${transaction.fromAccName || transaction.toAccName || "N/A"}` // Handle empty names
    ),
    escapeCsvField(
      transaction.cryptoWalletId
        ? transaction.transactionType === "SWAP"
          ? transaction.toAccName
          : transaction.wallet_address
        : `${transaction.fromAccNum || transaction.toAccNum || "N/A"} - ${
            transaction.fromBankName || transaction.toBankName || "N/A"
          }`
    ),
    escapeCsvField(
      new Date(transaction.createdAt)
        .toLocaleString(undefined, {
          day: "2-digit",
          month: "2-digit",
          year: "numeric",
          hour: "2-digit",
          minute: "2-digit",
          hour12: false,
        })
        .replace(",", "")
    ),
  ]);

  // each transaction is now being converted in to string
  const csvContent = [
    header.join(","),
    ...rows.map((row) => row.join(",")),
  ].join("\n");

  const blob = new Blob([csvContent], { type: "text/csv" });
  const url = URL.createObjectURL(blob);
  const link = document.createElement("a");

  link.download = "transactions.csv";
  link.href = url;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};
