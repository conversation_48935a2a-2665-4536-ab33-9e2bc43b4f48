import { useEffect, useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { ChevronDownIcon, CloseIcon, Hamburger, XMarkIcon } from "./icons";
import axios from "axios";
import { CONSTANTS, getToken } from "@/app/utils";
import { usePathname } from "next/navigation";

function Nav() {
  const [user, setUser] = useState(null);
  const [showMenu, setShowMenu] = useState(false);
  const pathname = usePathname();

  const onToggle = (selector: string) => () => {
    [...document.querySelectorAll(".mobile-dropDown")]
      .filter((node) => node.id != selector.slice(1))
      .forEach((dropDown) => dropDown.classList.add("hidden"));

    let elem = document.querySelector(selector);
    elem?.classList.toggle("hidden");
  };

  useEffect(() => {
    async function fetchAuth() {
      try {
        const res = await axios.get(CONSTANTS.SERVER_URL + "/v1/auth", {
          headers: { Authorization: "Bearer " + getToken() },
        });
        if (res.status === 403) return;
        else setUser(res.data.data);
      } catch (err: any) {
        if (err.response) {
          if (err.response.status === 403) {
            // ------ Not logged in
          }
        }
        console.log(err);
      }
    }

    fetchAuth();
  }, []);

  const MobileDropdownItem = ({
    href,
    children,
  }: {
    href: string;
    children: React.ReactNode;
  }) => {
    return (
      <li>
        <Link href={href} className="block px-4 py-2 hover:bg-zinc-100">
          {children}
        </Link>
      </li>
    );
  };

  const MobileDropdown = ({
    id,
    title,
    items,
    onToggle,
  }: {
    id: string;
    title: string;
    items: { href: string; label: string }[];
    onToggle: (selector: string) => () => void;
  }) => {
    const dropdownId = `#${id}`;
    return (
      <li onClick={onToggle(dropdownId)}>
        <Link
          href={"#"}
          className="flex gap-1 justify-between items-center py-3"
        >
          <span>{title}</span>
          <ChevronDownIcon className="w-5 h-4 mt-1" />
        </Link>

        <div
          id={id}
          className={`mobile-dropDown hidden top-[4.3rem] z-10 font-normal`}
        >
          <ul className="text-sm text-gray-700">
            {items.map((item, index) => (
              <MobileDropdownItem href={item.href} key={index}>
                {item.label}
              </MobileDropdownItem>
            ))}
          </ul>
        </div>
      </li>
    );
  };

  return (
    <div className="border-b border-zinc-200 shadow bg-white font-circularStd">
      <div className="max-w-7xl mx-auto px-4 py-4 flex items-center justify-between">
        <Link href={"/"} className="logo">
          <Image
            src={"/images/flincap-logo.svg"}
            alt="."
            width={100}
            height={50}
          />
        </Link>

        <div className="links hidden md:flex gap-8 items-center text-app">
          <ul className="flex gap-7 items-center"></ul>
          <div className="flex gap-8 items-center">
            <Link href={"/"}>Home</Link>
            <Link
              target="_blank"
              href="https://substack.com/@flincapnewsletter?utm_campaign=profile&utm_medium=profile-page"
            >
              Blog
            </Link>
            {user ? (
              <>
                <Link
                  href="/dashboard"
                  className="rounded-md px-10 py-3 bg-black text-white text-md"
                >
                  Dashboard
                </Link>
              </>
            ) : (
              <div className="flex gap-4 items-center">
                <Link
                  href="https://forms.gle/HmaHKiMd2BJ1SGZg7"
                  className="rounded-md px-10 py-3 bg-appPurple text-white text-md"
                >
                  Request an account
                </Link>
                <Link
                  href="/auth/login"
                  className="rounded-md px-10 py-3 text-appPurple text-md border border-appPurple"
                >
                  Sign in
                </Link>
              </div>
            )}
          </div>
        </div>

        <button
          className="burger block md:hidden"
          onClick={() => setShowMenu((prev) => !prev)}
        >
          {showMenu ? (
            <XMarkIcon className="w-8 h-8" />
          ) : (
            <Hamburger className="w-4 h-4" />
          )}
        </button>
        {showMenu ? (
          <ul className="md:hidden absolute z-10 bg-appWhite left-0 right-0 top-[82px] border-b shadow-sm px-4 py-5 flex flex-col gap-3">
            <MobileDropdown
              id="mProductDropDown"
              items={[
                { href: "/desk", label: "Trading Desk" },
                { href: "/paymentapis", label: "Payment for Crypto APIs" },
              ]}
              title="Products"
              onToggle={onToggle}
            />
            <MobileDropdown
              id="mDeveloperDropDown"
              items={[
                { href: "#", label: "Guides" },
                { href: "#", label: "API reference" },
              ]}
              title="Developers"
              onToggle={onToggle}
            />
            <li>
              <Link
                href={"/sell"}
                target="_blank"
                className={`flex gap-1 items-center py-3 ${
                  pathname === "/sell" ? "text-appPurple" : ""
                }`}
              >
                <span>Sell</span>
              </Link>
            </li>
            <li className="mb-4 mt-4">
              {user ? (
                <Link
                  href="/dashboard"
                  className="rounded-md px-10 py-3 bg-black text-white text-md block w-full text-center"
                >
                  Dashboard
                </Link>
              ) : (
                <div className="space-y-2">
                  <Link
                    href="https://forms.gle/HmaHKiMd2BJ1SGZg7"
                    className="rounded-md px-10 py-3 bg-appPurple text-white text-md block w-full text-center"
                  >
                    Sign up
                  </Link>
                  <Link
                    href="/auth/login"
                    className="rounded-md px-10 py-3 border border-appPurple text-appPurple text-md block w-full text-center"
                  >
                    Sign in
                  </Link>
                </div>
              )}
            </li>
          </ul>
        ) : null}
      </div>
    </div>
  );
}

export default Nav;
