"use client";
import { EditIcon, XMarkIcon } from "@/components/icons";
import useStore from "@/store";
import { useEffect, useState } from "react";
import { ToastContainer, toast } from "react-toastify";
import { adminApi, api } from "../utils";
import {
  CreateAssetFeesModal,
  UpdateAssetFeesModal,
} from "@/components/modals/AssetFeesModal";
import { useAdminAuth } from "@/lib/useAdminAuth";
import LoadingSpinner from "@/components/LoadingSpinner";

function Page() {
  const { isLoading, isAuthenticated, isAdmin } = useAdminAuth();
  const { setModal } = useStore();
  const [assetFees, setAssetFees] = useState<any[]>([]);

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <LoadingSpinner />
      </div>
    );
  }

  // This should not render if auth fails (user will be redirected)
  // But adding as extra safety
  if (!isAuthenticated || !isAdmin) {
    return null;
  }

  useEffect(() => {
    async function fetchAssetFees() {
      try {
        const res = await api.get("/v1/wallets/asset_fees");
        console.log(res);
        setAssetFees(res.data.data);
      } catch (err) {
        console.log(err);
      }
    }

    fetchAssetFees();
  }, []);

  return (
    <div>
      <section className="pt-4">
        <div className="cards grid md:grid-cols-4 gap-4 px-4 my-4">
          <div className="card rounded-md border hover:border-[1px] hover:shadow px-4 py-6 text-sm">
            <h3 className="font-semibold">Total Users</h3>
            <h1 className="font-semibold text-xl mt-6">200</h1>
          </div>
          <div className="card rounded-md border hover:border-[1px] hover:shadow px-4 py-6 text-sm">
            <h3 className="font-semibold">No. of Transactions</h3>
            <h1 className="font-semibold text-xl mt-6">3k</h1>
          </div>
          <div className="card rounded-md border hover:border-[1px] hover:shadow px-4 py-6 text-sm">
            <h3 className="font-semibold">Total Credit Applications</h3>
            <h1 className="font-semibold text-xl mt-6">459</h1>
          </div>
          <div className="card rounded-md border hover:border-[1px] hover:shadow px-4 py-6 text-sm">
            <h3 className="font-semibold">Total Compliance checks</h3>
            <h1 className="font-semibold text-xl mt-6">53</h1>
          </div>
        </div>
      </section>

      <div className="pt-12 max-w-5xl px-4">
        <div className="flex items-cemter justify-between">
          <h1 className="text-xl font-semibold leading-8">Asset Fees</h1>
        </div>

        <button
          onClick={() => setModal(<CreateAssetFeesModal />)}
          className="py-[7px] border-0 border-prim rounded text-ash text-sm bg-ash1 align-middle"
        >
          <div className="font-medium align-middle flex flex-row gap-[12px] bg-appPurple text-white p-3 rounded">
            Create asset fee
          </div>
        </button>

        <div className="mt-2 flex flex-col h-72 w-max overflow-x-hidden overflow-y-auto">
          <div className="sm:-mx-6 lg:-mx-8">
            <div className="inline-block min-h-full py-2 sm:px-6 lg:px-8">
              <div className="overflow-y-auto shadow border border-[#DADCE0] bg-white rounded-lg">
                <table className="min-h-full text-left text-sm font-light">
                  <thead className="font-medium dark:border-neutral-500 text-ash rounded-lg bg-ash1 border-b border-b-ash2">
                    <tr className="uppercase">
                      <th scope="col" className="px-6 py-4">
                        name
                      </th>
                      <th scope="col" className="px-6 py-4">
                        fee
                      </th>
                      <th scope="col" className="px-6 py-4">
                        action
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {assetFees?.map((asset) => (
                      <tr
                        className="border-b dark:border-neutral-500"
                        key={asset.id}
                      >
                        <td className="whitespace-nowrap px-6 py-4 font-medium">
                          {asset.name}
                        </td>
                        <td className="whitespace-nowrap px-6 py-4">
                          {asset.fee}
                        </td>
                        <td className="whitespace-nowrap px-6 py-4 flex gap-3">
                          <button
                            onClick={() =>
                              setModal(<UpdateAssetFeesModal asset={asset} />)
                            }
                            className=""
                            type="button"
                          >
                            <EditIcon width={20} height={20} />
                          </button>
                          <button
                            onClick={async () => {
                              const { data } = await adminApi.delete(
                                `/v1/admin/asset-fees/${asset.id}`
                              );

                              if (data.success)
                                return toast.success(data.message);
                              return toast.error(data.data.message);
                            }}
                            className=""
                            type="button"
                          >
                            <XMarkIcon className="w-5" />
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Page;
